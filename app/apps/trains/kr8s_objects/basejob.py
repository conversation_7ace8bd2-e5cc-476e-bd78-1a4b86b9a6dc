from kr8s import NotFoundError
from kr8s.objects import APIObject, NetworkPolicy, Pod, Service

import app
from app import logger
from app.apps.trains.exceptions import TrainReplicaTypeException
from app.core.exceptions import DuplicatePortException, InvalidTargetPortException, ResourceNotFoundException, TooManyPortsException
from app.core.qingcloud.common import get_qingcloud_user_info_by_user_id
from app.models.trains import Train
from app.core.constant import PRIORITY_CLASSES_JOBS
from app.core.models import QingcloudUser
from app.core.volumes.utils import get_envs_definitions  # , get_volume_mounts_definitions, get_volumes_definitions
from app.core.volumes.manager import VolumesDefinition


def get_special_envs(train: Train):
    # get user's name from iaas.
    user_info = get_qingcloud_user_info_by_user_id(train.user_id)
    env = [
        {
            "name": "AICP_PLATFORM",
            "value": app.settings.AICP_PLATFORM
        },
        {
            "name": "<PERSON>CP_TYPE",
            "value": "JOB"
        },
        {
            "name": "AICP_USER_NAME",
            "value": user_info.get("user_name")
        },
        {
            "name": "AICP_NAME",
            "value": train.name
        },
        {
            "name": "AICP_SPEC_COUNT",
            "value": str(train.replica_specs[0].replicas)
        },
        {
            "name": "AICP_SPEC_GPU",
            "value": str(train.replica_specs[0].custom_gpu * train.replica_specs[0].replicas)
        },
        {
            "name": "AICP_SPEC_CPU",
            "value": str(train.replica_specs[0].custom_cpu)
        },
        {
            "name": "AICP_SPEC_MEMORY",
            "value": str(train.replica_specs[0].custom_memory)
        },
        {
            "name": "AICP_SPEC_GPU_NAME",
            "value": str(train.replica_specs[0].custom_gpu_type)
        },
        {
            "name": "AICP_SPEC_GPU_MEMORY",
            "value": str(train.replica_specs[0].custom_gpu_memory)
        },
        {
            "name": "AICP_SPEC_GPU_TYPE",
            "value": train.replica_specs[0].resource.get_product_gpu_type()
        },
        {
            "name": "AICP_HOST_MACHINE",
            "valueFrom": {
                "fieldRef": {"fieldPath": "spec.nodeName"}
            }
        },
        {
            "name": "AICP_HOSTNAME",
            "valueFrom": {
                "fieldRef": {"fieldPath": "metadata.name"}
            }
        },
        {
            "name": "AICP_PROJECT_TYPE",
            "value": train.project_category_key
        }
    ]
    # private env
    if app.settings.ENV_VARIABLE_PREFIX_TRAIN != "":
        private_env = [
            {
                "name": "AICP_TYPE",
                "value": "JOB"
            },
            {
                "name": f"{app.settings.ENV_VARIABLE_PREFIX_TRAIN}_USER_NAME",
                "value": user_info.get("user_name")
            },
            {
                "name": f"{app.settings.ENV_VARIABLE_PREFIX_TRAIN}_NAME",
                "value": train.name
            },
            {
                "name": f"{app.settings.ENV_VARIABLE_PREFIX_TRAIN}_SPEC_COUNT",
                "value": str(train.replica_specs[0].replicas)
            },
            {
                "name": f"{app.settings.ENV_VARIABLE_PREFIX_TRAIN}_SPEC_GPU",
                "value": str(train.replica_specs[0].custom_gpu * train.replica_specs[0].replicas)
            },
            {
                "name": f"{app.settings.ENV_VARIABLE_PREFIX_TRAIN}_SPEC_CPU",
                "value": str(train.replica_specs[0].custom_cpu)
            },
            {
                "name": f"{app.settings.ENV_VARIABLE_PREFIX_TRAIN}_SPEC_MEMORY",
                "value": str(train.replica_specs[0].custom_memory)
            },
            {
                "name": f"{app.settings.ENV_VARIABLE_PREFIX_TRAIN}_SPEC_GPU_NAME",
                "value": str(train.replica_specs[0].custom_gpu_type)
            },
            {
                "name": f"{app.settings.ENV_VARIABLE_PREFIX_TRAIN}_SPEC_GPU_MEMORY",
                "value": str(train.replica_specs[0].custom_gpu_memory)
            },
            {
                "name": f"{app.settings.ENV_VARIABLE_PREFIX_TRAIN}_SPEC_GPU_TYPE",
                "value": train.replica_specs[0].resource.get_product_gpu_type()
            },
            {
                "name": f"{app.settings.ENV_VARIABLE_PREFIX_TRAIN}_HOST_MACHINE",
                "valueFrom": {
                    "fieldRef": {"fieldPath": "spec.nodeName"}
                }
            },
            {
                "name": f"{app.settings.ENV_VARIABLE_PREFIX_TRAIN}_HOSTNAME",
                "valueFrom": {
                    "fieldRef": {"fieldPath": "metadata.name"}
                }
            },
            {
                "name": f"{app.settings.ENV_VARIABLE_PREFIX_TRAIN}_PROJECT_TYPE",
                "value": train.project_category_key
            }
        ]
        env.extend(private_env)
    return env


class BaseJob(APIObject):
    version = "aicp.org/v1"
    endpoint = "This field should be override in subclass"
    kind = "This field should be override in subclass"
    plural = "This field should be override in subclass"
    singular = "This field should be override in subclass"
    namespaced = False

    replica_specs_name = None
    replica_types = []
    replica_containers_name = None

    @property
    def is_suspend(self):
        """
        检查作业是否暂停
        :return:
        """
        return self.spec.get("runPolicy", {}).get("suspend", False)

    def suspend(self):
        """
        暂停任务
        :return:
        """
        logger.info(f"Suspend Job Patch {self.name} ")
        if self.is_suspend:
            logger.info(f"Job {self.name} is already suspended")
            return
        self.patch(
            [{"op": "replace", "path": "/spec/runPolicy/suspend", "value": True}],
            type="json"
        )

    def restart(self):
        """
        重启任务
        :return:
        """
        if not self.is_suspend:
            logger.info(f"Job {self.name} is already running")
            return
        self.patch(
            [{"op": "remove", "path": "/spec/runPolicy/suspend"}],
            type="json"
        )

    def get_pod(self, pod_name) -> Pod:
        try:
            pod = Pod.get(pod_name, namespace=self.namespace)
            return pod  # noqa
        except NotFoundError as e:
            raise ResourceNotFoundException(pod_name)

    def get_pod_seletor(self, pod: Pod):
        return {
            "training.kubeflow.org/job-name": pod.labels["training.kubeflow.org/job-name"],
            "training.kubeflow.org/job-role": pod.labels["training.kubeflow.org/job-role"],
            "training.kubeflow.org/operator-name": pod.labels["training.kubeflow.org/operator-name"],
            "training.kubeflow.org/replica-index": pod.labels["training.kubeflow.org/replica-index"],
            "training.kubeflow.org/replica-type": pod.labels["training.kubeflow.org/replica-type"],
        }

    def get_node_port_service_name(self, pod: Pod) -> str:
        return f"{pod.name}-node-port"

    def get_node_port_service(self, pod: Pod) -> Service:
        """

        :return:
        """
        return Service.get(self.get_node_port_service_name(pod), namespace=self.namespace)

    def get_network_policy_name(self, pod: Pod):
        return f"{pod.name}-network-policy"

    def get_network_policy(self, pod: Pod):
        """

        :return:
        """
        return NetworkPolicy.get(self.get_network_policy_name(pod), namespace=self.namespace)

    def _patch_add_node_port(self, pod: Pod, target_port: int):
        if not 0 < target_port < 65536:
            raise InvalidTargetPortException(target_port)

        service = self.get_node_port_service(pod)
        ports = [port.port for port in service.spec.ports]
        if 0 < app.settings.NODE_PORT_PER_NOTEBOOK <= len(ports):
            raise TooManyPortsException()

        if target_port in ports:
            raise DuplicatePortException(target_port)

        service.patch(
            [
                {
                    "op": "add",
                    "path": "/spec/ports/-",
                    "value": {
                        "name": f"port-{target_port}",
                        "port": target_port,
                        "targetPort": target_port,
                    }
                }
            ],
            type="json"
        )

    def _patch_remove_node_port(self, pod: Pod, target_port: int):
        service: Service
        try:
            service = self.get_node_port_service(pod)
        except NotFoundError as e:
            logger.warning(f"node port service not found: {pod.name}")
            return

        ports = [port.port for port in service.spec.ports]
        if target_port not in ports:
            logger.warning(f"node port {target_port} not found in service {service.name}")
            return

        if len(ports) == 1:
            logger.info(f"delete node port service {service.name}, because no port left.")
            service.delete()
        else:
            service.patch(
                [
                    {
                        "op": "remove",
                        "path": f"/spec/ports/{[port.port for port in service.spec.ports].index(target_port)}"
                    }
                ],
                type="json"
            )

    def _patch_add_network_policy(self, pod: Pod, target_port: int):
        if not app.settings.USE_NETWORK_POLICY:
            logger.info("network policy is disabled")
            return

        if not 0 < target_port < 65536:
            raise InvalidTargetPortException(target_port)

        network_policy = self.get_network_policy(pod)
        network_policy.patch(
            [
                {
                    "op": "add",
                    "path": "/spec/ingress/-",
                    "value": {
                        "ports": [
                            {
                                "port": target_port,
                                "protocol": "TCP"
                            }
                        ]
                    }
                }
            ],
            type="json"
        )

    def _patch_remove_network_policy(self, pod: Pod, target_port: int):
        if not app.settings.USE_NETWORK_POLICY:
            logger.info("network policy is disabled")
            return
        try:
            network_policy = self.get_network_policy(pod)
        except NotFoundError as e:
            logger.warning(f"network policy not found: {pod.name}")
            return

        ports = [port.port for port in network_policy.spec.ingress[0].ports]
        if target_port not in ports:
            logger.warning(f"node port {target_port} not found in network policy {pod.name}")
            return

        if len(ports) == 1:
            logger.info(f"delete network policy {pod.name}, because no port left.")
            network_policy.delete()
        else:
            network_policy.patch(
                [
                    {
                        "op": "remove",
                        "path": f"/spec/ingress/0/ports/{ports.index(target_port)}"
                    }
                ],
                type="json"
            )

    def create_node_port_service(self, pod: Pod, target_port: int):
        """

        :param pod:
        :param protocol:
        :param target_port:
        :return:
        """
        service = Service(
            {
                "apiVersion": "v1",
                "kind": "Service",
                "metadata": {
                    "name": self.get_node_port_service_name(pod),
                    "namespace": self.namespace,
                    "labels": self.get_pod_seletor(pod),
                },
                "spec": {
                    "type": "NodePort",
                    "selector": self.get_pod_seletor(pod),
                    "ports": [
                        {
                            "name": f"port-{target_port}",
                            "port": target_port,
                            "targetPort": target_port,
                        }
                    ]
                }
            }
        )
        service.create()
        service.set_owner(self)
        return service

    def create_network_policy(self, pod: Pod, target_port: int):
        """

        :param pod:
        :param target_port:
        :return:
        """
        network_policy = NetworkPolicy(
            {
                "apiVersion": "networking.k8s.io/v1",
                "kind": "NetworkPolicy",
                "metadata": {
                    "name": self.get_network_policy_name(pod),
                    "namespace": self.namespace,
                    "labels": self.get_pod_seletor(pod),
                },
                "spec": {
                    "podSelector": {
                        "matchLabels": self.get_pod_seletor(pod),
                    },
                    "policyTypes": [
                        "Ingress"
                    ],
                    "ingress": [
                        {
                            "ports": [
                                {
                                    "port": target_port,
                                    "protocol": "TCP"
                                }
                            ]
                        }
                    ]
                }
            }
        )
        network_policy.create()
        network_policy.set_owner(self)
        return network_policy

    def add_node_port(self, pod_name: str, target_port: int):
        """
        添加 node port  并且开放网络策略
        :param pod_name: str
        :param target_port:
        """
        pod = self.get_pod(pod_name)
        logger.info(f"add node port {target_port} for pod {pod}")
        if not 0 < target_port < 65536:
            raise InvalidTargetPortException(target_port)

        try:
            self._patch_add_node_port(pod, target_port)
        except NotFoundError as e:
            logger.info(f"create node port service for pod {pod}")
            self.create_node_port_service(pod, target_port)

        try:
            self._patch_add_network_policy(pod, target_port)
        except NotFoundError as e:
            logger.info(f"create network policy for pod {pod}")
            self.create_network_policy(pod, target_port)

    def remove_node_port(self, pod_name: str, target_port: int):
        """
        移除 node port 以及网络策略
        :param pod_name:
        :param target_port:
        """
        pod = self.get_pod(pod_name)
        logger.info(f"remove node port {target_port} for pod_name {pod_name}")
        self._patch_remove_node_port(pod, target_port)
        self._patch_remove_network_policy(pod, target_port)

    @classmethod
    def get_resource_definition(cls, train: Train, user: QingcloudUser, docker_secret: str = None) -> dict:
        """
        获取资源定义
        :return:
        """
        definition = {
            "apiVersion": cls.version,
            "kind": cls.kind,
            "metadata": {
                "name": train.uuid,
                "namespace": train.namespace,
                "labels": {
                    "app": train.uuid,
                    "user": train.user_id,
                    "user_name": train.user_name,
                    "train_uuid": str(train.uuid),
                    "aicp.group/workload": "container",
                    "aicp/api-version": app.settings.version
                },
                "annotations": {
                    "sidecar.istio.io/inject": "false"
                }
            },
            "spec": {
                "runPolicy": {
                    # delete job immediately after 60s, fix bug
                    "ttlSecondsAfterFinished": train.ttl_seconds_after_finished if train.ttl_seconds_after_finished < 60 else 60,
                    # backoffLimit = 重试次数 + 1
                    "backoffLimit": train.backoff_limit + 1,
                    # When job finished，clean Running/Pending pod，keep Failed/Succeeded pod until job deleted
                    "cleanPodPolicy": "Running",
                    # volcano scheduler config
                    "schedulingPolicy": {
                        "priorityClass": PRIORITY_CLASSES_JOBS[train.priority - 1]
                    }
                },
                cls.replica_specs_name: {}
            }
        }

        if train.active_deadline_seconds:
            definition["spec"]["runPolicy"]["activeDeadlineSeconds"] = train.active_deadline_seconds + 10

        volumes_manager = VolumesDefinition(user, train.namespace, train.volume_specs)
        volumes_definitions = volumes_manager.get_volumes_definition()
        volume_mounts_definitions = volumes_manager.get_volumes_mounts_definition()

        special_envs = get_special_envs(train)
        envs = get_envs_definitions(train.envs, special_envs)

        for replica in train.replica_specs:
            if replica.replica_type not in cls.replica_types:
                logger.error(f'replica_type {replica.replica_type} not in {cls.replica_types}')
                raise TrainReplicaTypeException(replica.replica_type)

            replica.resource.append_envs(envs)
            command = ["/bin/bash", "-c", train.command.lstrip("bash")] if train.command.startswith("bash") else ["/bin/sh", "-c", train.command]
            replica_type_definition = {
                "replicas": replica.replicas,
                "restartPolicy": "OnFailure" if train.restart_policy != "Never" else "Never",
                "template": {
                    "metadata": {
                        "annotations": {
                            "sidecar.istio.io/inject": "false"
                        },
                        "labels": {
                            "app": train.uuid,
                            "user": train.user_id,
                            "user_name": train.user_name,
                            "train_uuid": str(train.uuid),
                            "aicp.group/workload": "container",
                        }
                    },
                    "spec": {
                        "volumes": volumes_definitions,
                        "schedulerName": train.replica_specs[0].resource.get_scheduler_name(),
                        "containers": [
                            {
                                "securityContext": {
                                    "capabilities": {
                                        "add": [
                                            "IPC_LOCK",
                                            "SYS_RESOURCE"
                                        ]
                                    },
                                },
                                "name": cls.replica_containers_name,
                                "imagePullPolicy": "Always",
                                "image": train.image_url,
                                "command": command,
                                "resources": replica.resource.get_k8s_resources_definition(),
                                "volumeMounts": volume_mounts_definitions,
                                "env": envs,
                            }
                        ]
                    }
                }
            }
            if train.active_deadline_seconds:
                replica_type_definition["template"]["spec"]["activeDeadlineSeconds"] = train.active_deadline_seconds + 10

            ib_annotations = replica.resource.get_ib_annotations(train.namespace)
            if ib_annotations:
                replica_type_definition["template"]["metadata"]["annotations"].update(ib_annotations)

            node_selector = replica.resource.get_k8s_node_selector_configuration()
            if node_selector:
                replica_type_definition["template"]["spec"]["nodeSelector"] = node_selector

            node_affinity = replica.resource.get_k8s_affinity_configuration()
            if node_affinity:
                replica_type_definition["template"]["spec"]["affinity"] = node_affinity

            toleration = replica.resource.get_k8s_toleration_configuration()
            if toleration:
                replica_type_definition["template"]["spec"]["tolerations"] = toleration

            secret_name = train.create_secret(train.namespace)
            if secret_name:
                replica_type_definition["template"]["spec"]["imagePullSecrets"] = [{"name": secret_name}]
            elif docker_secret:
                replica_type_definition["template"]["spec"]["imagePullSecrets"] = [{"name": docker_secret}]

            definition["spec"][cls.replica_specs_name][replica.replica_type] = replica_type_definition

        return definition
