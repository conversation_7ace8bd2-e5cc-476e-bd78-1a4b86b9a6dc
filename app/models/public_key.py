# cython: skip
from typing import List,Dict
from datetime import datetime
from pydantic import BaseModel
from sqlmodel import SQLModel, Field



from app.core.models import UserIdModel


class PublicKeyBase(SQLModel, table=False):
    pk_id: str = Field(default=None, primary_key=True)
    key: str = Field('', description="公钥")
    user_id: str = Field('', description="用户")
    root_user_id: str = Field('', description="用户")
    create_time: datetime = Field('', description="创建时间")
    description: str = Field('', description="公钥说明")
    name: str = Field(None, description="公钥名称")


class PublicKey(PublicKeyBase, UserIdModel, table=True):
    __tablename__ = "public_key"


class PublicKeyDetailRep(PublicKeyBase):
    total: int = 0


class PublicKeyCreate(BaseModel):
    key: str = ''
    description: str = ''
    name: str = None

class PublicKeyResponse(BaseModel):
    data: Dict[str, List[Dict]]