# cython: skip
from datetime import datetime

from pydantic import BaseModel
from sqlalchemy import text, JSON
from sqlmodel import Field, SQLModel
from typing import Dict, Optional

import app
from app.core.models import ActiveRecordMixin, AutomaticIdModel, UUIDModel, TimestampModel


class SharedPermissionEnum:
    Edit = "edit"
    View = "view"


class UserInfoSchema(BaseModel):
    has_auth: bool
    user_id: str
    user_name: str
    has_work_group: bool = False


class MinioKeyBase(SQLModel, table=False):
    namespace: str = Field(..., nullable=False, description="namespace")
    access_key: str = Field(..., nullable=False, description="access_key")


class MinioKey(MinioKeyBase, UUIDModel, table=True):
    secret_key: str = Field(..., nullable=False, description="secret_key")

    __tablename__ = "minio_key"


class ProjectCategoryBase(SQLModel, table=False):
    name: str = Field(..., nullable=False, description="name")
    key: str = Field(..., nullable=False, description="key", sa_column_kwargs={"server_default": "''"})


class ProjectCategory(AutomaticIdModel, ProjectCategoryBase, table=True):
    owner: str = Field(..., nullable=False, description="owner")

    __tablename__ = "project_category"


class UserStatusEnum:
    active = "active"
    inactive = "inactive"
    deleted = "deleted"
    creating = "creating"
    warning = "warning"


class UserInfo(TimestampModel, ActiveRecordMixin, table=True):
    user_id: str = Field(primary_key=True, description="user_id")
    namespace: str = Field(..., nullable=False, description="namespace")
    containers_number: int = Field(app.settings.POD_NUMBER, nullable=False, description="containers_number")
    jobs_number: int = Field(app.settings.JOB_NUMBER, nullable=False, description="job_number")
    rg_nodes: int = Field(app.settings.RG_NODES, nullable=False, description="rg_nodes")
    maas_server: int = Field(app.settings.MAAS_SERVER, nullable=False, description="maas_server_num", sa_column_kwargs={
        "server_default": text("10")
    })
    status: str = Field(..., nullable=False, description="warning为profile未创建成功")
    priority: int = Field(3, nullable=False, description="用户的训练任务优先级 1-10 数值越高优先级越高",
                          sa_column_kwargs={
                              "server_default": text("3")
                          })
    # 目录数量 默认为1
    dir_number: int = Field(app.settings.DIR_NUMBER, nullable=False, description="dir_number", sa_column_kwargs={
        "server_default": text("1")
    })
    # 存储容量 默认为2T
    capacity: int = Field(app.settings.CAPACITY, nullable=False, description="capacity", sa_column_kwargs={
        "server_default": text("2048")
    })
    last_login_at: datetime = Field(nullable=False, description="last_login_at",
                                    sa_column_kwargs={
                                        "server_default": text("current_timestamp(0)")
                                    })
    # GPU 配额限制,默认80
    gpu_number: int = Field(app.settings.GPU_NUMBER, nullable=False, description="GPU quota",
                            sa_column_kwargs={"server_default": text("1")})

    gpu_model_quotas: Optional[Dict[str, int]] = Field(None, nullable=True, description="vgpu 配额列表", sa_type=JSON, )
    # vGPU 配额限制,默认80
    vgpu_number: int = Field(app.settings.GPU_NUMBER, nullable=False, description="vGPU quota",
                             sa_column_kwargs={"server_default": text("1")})
    vgpu_model_quotas: Optional[Dict[str, int]] = Field(None, nullable=True, description="vgpu 配额列表", sa_type=JSON, )
    # 镜像仓库配额限制,默认100G
    docker_quota: int = Field(app.settings.DOCKER_QUOTA, nullable=False, description="镜像仓库大小,默认100G",
                              sa_column_kwargs={"server_default": text("100")})
    # 用户可建项目数 默认为1
    project_number: int = Field(app.settings.PROJECT_NUMBER, nullable=False, description="docker_number", sa_column_kwargs={
        "server_default": text("10")
    })

    __tablename__ = "user_info"
    # user_name: str = Field(..., nullable=False, description="user_name")
    # email: str = Field(..., nullable=False, description="email")
    # status: str = Field(..., nullable=False, description="status")


class UserBaseInfo(BaseModel):
    user_id: str = None
    root_user_id: str = None
    email: str = None
    notify_email: str = None
    phone: str = None
    user_name: str = None
