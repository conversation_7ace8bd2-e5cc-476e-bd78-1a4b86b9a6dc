# cython: skip
import uuid
from enum import Enum
from typing import List, Optional
import pydantic
from pydantic import BaseModel
from sqlalchemy import text
from sqlmodel import SQLModel, Field

from app import logger
from app.core.config import coroutine_id, get_coroutine_id_value, get_request_id_context_value, \
    get_user_id_context_value, request_id_context, \
    user_id_context
from app.core.db import get_db_context_session
from app.core.models import ActiveRecordMixin, TimestampModel, UserIdModel, StatusModel
from app.core.utils import get_model_pk_v, id_worker


class NotebookMessageStopAction(str, Enum):
    NowStop = "0S_STOP"
    Minutes_10_Stop = "10M_STOP"
    Hours_2_Stop = "2H_STOP"
    Days_1_Stop = "1D_STOP"


class NotebookMessageReleaseAction(str, Enum):
    NowRelease = "0S_RELEASE"
    Minutes_10_Release = "10M_RELEASE"
    Hours_2_Release = "2H_RELEASE"
    Days_1_Release = "1D_RELEASE"


class TrainMessageStatusAction(str, Enum):
    Train_Done = "TR_DONE"
    Train_Failed = "TR_FAILED"
    Train_TERMINATED = "TR_TERMINATED"


class OperationRecordBase(SQLModel, table=False):
    operation_id: str = Field(
        default_factory=lambda: "op-" + id_worker.get_hex_id(),
        primary_key=True,
        index=True,
        nullable=False,
        max_length=36,
    )
    action: str = Field('', description="操作类型")
    resource: str = Field('', description="资源")
    child_resource: str = Field('', description="衍生资源")
    request_id: Optional[str] = Field(
        default_factory=lambda: get_request_id_context_value() or get_coroutine_id_value() or None,
        description="请求ID", nullable=True)
    params: Optional[str] = Field('', description="参数", nullable=True)


class OperationRecord(OperationRecordBase, TimestampModel, StatusModel, UserIdModel, ActiveRecordMixin, table=True):
    __tablename__ = "operation_record"

    @classmethod
    def create_by_resource(cls, resource, **kwargs):
        """
        resource : Notebook or Train Model

        :param resource:
        :return:
        """
        user_id = get_user_id_context_value("System")
        operation_record_args = {
            'user_id': user_id,
            'resource': get_model_pk_v(resource),
            'status': resource.status,
            'reason': resource.reason,
            'action': resource.status
        }
        if kwargs:
            operation_record_args.update(kwargs)
        return OperationRecord(**operation_record_args)

    @classmethod
    def create_by_user_info(cls, user_info, **kwargs):
        """
        user_info : UserInfo Model

        :param user_info:
        :return:
        """
        operation_record_args = {
            'user_id': 'system',
            'resource': user_info.namespace,
            'status': user_info.status,
            'reason': user_info.status,
            'action': user_info.status
        }
        operation_record_args.update(kwargs)
        return OperationRecord(**operation_record_args)

    def insert(self):
        with get_db_context_session() as session:
            session.add(self)
            session.commit()
            session.refresh(self)
            return self

    def update(self, **kwargs):
        with get_db_context_session() as session:
            session.add(self)
            for key, value in kwargs.items():
                setattr(self, key, value)
            session.commit()
            session.refresh(self)
            return self




