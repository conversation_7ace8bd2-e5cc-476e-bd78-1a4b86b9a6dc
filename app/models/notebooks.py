# cython: skip
import json
import math
from datetime import datetime
from enum import Enum
from functools import cached_property
from typing import Dict, List, Optional, Tuple

from fastapi import Query
from pydantic import BaseModel, validator
from pydantic.datetime_parse import timed<PERSON>ta
from sqlalchemy import Column, JSON, inspect
from sqlmodel import Field, Relationship, SQLModel
import app
from app import logger
from app.core.async_pool import get_async_pool
from app.core.config import LocalPathStorageMount
from app.core.constant import UserFrom
from app.core.exceptions import VolumeCreateException
from app.core.kube.api import STATUS_PHASE
from app.core.models import ActiveRecordMixin, AicpServers, AutomaticIdModel, BillingBase, CommonSearchQueryParams, \
    CustomImageSecret, EnvModel, \
    ImageModel, \
    KubernetesObjectUUIDModel, NameSpaceModel, QingcloudUser, ReplicaSpecBase, ReplicaSpecCreate, StatusModel, \
    TimestampModel, \
    UserIdModel, User<PERSON>atchInfoModel, VolumeSpecBase, VolumeSpecCreate
from app.core.qingcloud.billing import AsyncQAIBillingService, QAIBillingService
from app.core.qingcloud.common import get_qingcloud_user_info_by_user_id, send_to_push_server
from app.core.qingcloud.exceptions import BillingServiceException
from app.core.qingcloud.interface import ZONE_INFO, send_message_request
from app.core.utils import gpu_manager, id_worker
from app.core.volumes.manager import VolumesDefinition, VolumesManager


class NotebookStatus(str, Enum):
    Warning = "Warning"
    Pending = "Pending"
    Creating = "Creating"
    Created = "Created"
    CreateFailed = "CreateFailed"
    Running = "Running"
    Succeeded = "Succeeded"
    Failed = "Failed"
    Suspending = "Suspending"
    Suspended = "Suspended"
    Restarting = "Restarting"
    Terminating = "Terminating"
    Terminated = "Terminated"
    Unschedulable = "Unschedulable"
    Paused = "Paused"  # 仅返回给计费使用
    Unknown = "Unknown"


class NotebookReason(str, Enum):
    waiting_for_scheduling = "等待调度中"
    suspend = "已暂停"
    suspending = "暂停中"
    suspending_by_user = "用户暂停中"
    terminating = "释放中"
    running = "运行中"
    restarting = "重启中"
    initializing = "资源初始化中"
    unschedulable = "当前规格资源不足"
    pulling_image = "镜像拉取中"
    pulling_image_failed = "镜像拉取失败"
    image_building = "镜像构建中"
    image_build_failed = "镜像构建失败"
    suspend_by_billing = "欠费暂停"
    image_build_success = "镜像构建成功"
    mounting_failed = "存储挂载失败"
    unknown = "未知"


ALLNotebookStatus = [
    NotebookStatus.Warning, NotebookStatus.Pending, NotebookStatus.Creating, NotebookStatus.Created,
    NotebookStatus.CreateFailed, NotebookStatus.Running, NotebookStatus.Succeeded, NotebookStatus.Restarting,
    NotebookStatus.Terminated, NotebookStatus.Terminating, NotebookStatus.Suspending, NotebookStatus.Suspended,
    NotebookStatus.Unknown
]

SearchableNotebookStatus = [
    NotebookStatus.Warning, NotebookStatus.Pending, NotebookStatus.Creating, NotebookStatus.Created,
    NotebookStatus.Running, NotebookStatus.Succeeded, NotebookStatus.Suspended, NotebookStatus.CreateFailed,
    NotebookStatus.Terminating, NotebookStatus.Suspending, NotebookStatus.Restarting,
]
DeletedStatus = [STATUS_PHASE.Terminated, STATUS_PHASE.CreateFailed]

RunningNotebookStatus = [
    NotebookStatus.Warning, NotebookStatus.Pending, NotebookStatus.Creating, NotebookStatus.Created,
    NotebookStatus.Running, NotebookStatus.Succeeded, NotebookStatus.Suspended,
]

NotebookStatusZhCN = {
    NotebookStatus.Pending: "创建容器实例",
    NotebookStatus.Creating: "创建容器实例",
    NotebookStatus.Created: "创建容器实例",
    NotebookStatus.CreateFailed: "创建容器实例失败",
    NotebookStatus.Running: "容器实例运行中",
    NotebookStatus.Succeeded: "创建容器实例成功",
    NotebookStatus.Failed: "创建容器实例失败",
    NotebookStatus.Suspending: "暂停容器实例",
    NotebookStatus.Suspended: "暂停容器实例",
    NotebookStatus.Restarting: "重启容器实例",
    NotebookStatus.Terminating: "释放容器实例",
    NotebookStatus.Terminated: "释放容器实例",
    NotebookStatus.Unschedulable: "当前规格资源不足",
    NotebookStatus.Unknown: "未知",
}


def receive_after_update_notebook(mapper, connection, target):
    try:
        status_insp = inspect(target).get_history("status", True)
        reason_insp = inspect(target).get_history("reason", True)
        if not (status_insp.has_changes() or reason_insp.has_changes()):
            # if status not change, not push event to push server
            return

        logger.debug(f"receive_after_update, push event to push server, [uuid={target.uuid},status={target.status}]")
        action = NotebookStatusZhCN.get(target.status)
        if action:
            async_pool = get_async_pool()
            async_pool.submit_sync(
                send_to_push_server,
                action, target.uuid, target.status, target.user_id, target.__tablename__, target.reason, target.uuid, target.operation_user_id,
                callback=None
            )
    except Exception as e:
        logger.error("push event to push server error, %s", e)


class NotebookVolumeSpec(VolumeSpecBase, AutomaticIdModel, ActiveRecordMixin, table=True):
    pvc_name: str = Field("", nullable=True, description="pvc名称, 不需要传入, 由系统自动生成")
    notebook_uuid: str = Field(..., nullable=False, description="开发机UUID", foreign_key="notebook.uuid")
    notebook: Optional["Notebook"] = Relationship(back_populates="volume_specs")

    __tablename__ = "notebook_volume_spec"


class NotebookReplicaSpec(ReplicaSpecBase, AutomaticIdModel, ActiveRecordMixin, table=True):
    notebook_uuid: str = Field(..., nullable=False, description="开发机UUID", foreign_key="notebook.uuid")
    notebook: Optional["Notebook"] = Relationship(
        sa_relationship_kwargs={'uselist': False},
        back_populates="replica_specs"
    )

    __tablename__ = "notebook_replica_spec"


class NotebookBase(ImageModel, NameSpaceModel, UserIdModel, BillingBase, table=False):
    name: str = Field("容器实例", nullable=True, description="开发机名称")
    namespace: str = Field(..., nullable=False, description="开发机命名空间")

    server_type: str = Field("jupyter", nullable=False, description="服务类型: jupyter, code_server, rstudio")
    envs: List[EnvModel] = Field([], sa_column=Column(JSON), description="环境变量列表")
    stop_time: Optional[datetime] = Field(None, nullable=True, description="停止时间")
    auto_delete_time: Optional[datetime] = Field(None, nullable=True, description="删除时间")
    command: Optional[str] = Field(None, nullable=True, description="自定义启动命令")

    # noinspection PyMethodParameters
    @validator("envs")
    def val_envs(cls, val: List[EnvModel]):
        """

        :param val:
        :return:
        """
        if not val:
            return []
        return [v.dict() for v in val]

    class Config:
        arbitrary_types_allowed = True

        keep_untouched = (cached_property,)


NoteBookUUIDPrefix = "nb-"


class Notebook(NotebookBase, TimestampModel, UserIdModel, StatusModel, KubernetesObjectUUIDModel, ActiveRecordMixin,
               table=True):
    uuid: str = Field(
        default_factory=lambda: NoteBookUUIDPrefix + id_worker.get_hex_id(),
        primary_key=True,
        index=True,
        nullable=False,
        max_length=36,
    )
    server: Optional[str] = Field(None, nullable=True, description="服务地址, 用于前端进行跳转")
    volume_specs: List[NotebookVolumeSpec] = Relationship(back_populates="notebook")
    replica_specs: NotebookReplicaSpec = Relationship(
        sa_relationship_kwargs={'uselist': False},
        back_populates="notebook"
    )
    # pip源 pypi aliyun tsinghua huaweicloud
    pip: Optional[str] = Field(None, nullable=True, description="pip源")
    # conda源 conda aliyun tsinghua
    conda: Optional[str] = Field(None, nullable=True, description="conda源")
    # apt源 ubuntu aliyun tsinghua huaweicloud
    apt: Optional[str] = Field(None, nullable=True, description="apt源")
    operation_user_id: Optional[str] = Field(None, nullable=True, description="当前状态变更操作者")
    charge_status: Optional[str] = Field(None, nullable=True, description="当前计费状态")

    __eager__fields__ = ["volume_specs", "replica_specs"]

    def has_zfs_volume(self):
        for volume in self.volume_specs:
            if volume.volume_type == "LOCAL":
                return volume
        return None

    def push_message(self, action: str, status: str, reason: str = None):
        # push message to websocket
        try:
            send_to_push_server(action, self.uuid, status, self.user_id, self.__tablename__, reason, self.uuid, extend_user_id=self.operation_user_id)
        except Exception as e:
            logger.error(f"push message error: {e}")

    def send_message(self, action: str, error: str):
        # Send message to IAAS.
        if app.settings.IAAS_MESSAGE_ENABLE:
            try:
                user_info = get_qingcloud_user_info_by_user_id(self.user_id)
                send_data = {
                    "username": user_info.get("user_name"),
                    "zone": ZONE_INFO,
                    "name": self.name,
                    "id": self.uuid,
                    "spec": f"{self.replica_specs.custom_cpu}核 {self.replica_specs.custom_memory}G {self.replica_specs.custom_gpu_type} {self.replica_specs.custom_gpu_memory}G * {self.replica_specs.custom_gpu}" if self.replica_specs.custom_gpu > 0 else f"{self.replica_specs.custom_cpu}核 {self.replica_specs.custom_memory}G",
                    "action": action
                }
                send_message_request(error, self.user_id, json.dumps(send_data))
            except Exception as e:
                logger.exception(f"send message error: {e}")

    def get_gpu_vendor(self):
        """
        get gpu type
        :return:
        """
        if self.replica_specs.custom_aipods_type and self.replica_specs.custom_aipods_type.upper() == "VGPU":
            return "VGPU"
        if self.replica_specs.custom_aipods_type and self.replica_specs.custom_aipods_type.upper() == "VDCU":
            return "VDCU"
        gpu = gpu_manager.get_gpu_matcher(self.replica_specs.custom_gpu_type)
        if gpu:
            return gpu.vendor
        return None

    def get_zfs_volume(self):
        for volume in self.volume_specs:
            if volume.volume_type == "LOCAL":
                return volume
        return None

    def is_volumes_healthy(self, session=None) -> Tuple[bool, str]:
        user = QingcloudUser(**get_qingcloud_user_info_by_user_id(self.user_id), user_from=UserFrom.CONSOLE)
        for v in self.volume_specs:
            if v.volume_type == "MINIO":
                v.delete(session_=session)
        exists, fileset = VolumesDefinition(user, self.namespace, self.volume_specs).exists()
        return exists, fileset

    def set_volume_specs(self, user, volume_specs_create: List[VolumeSpecCreate]):
        self.replica_specs.resource.init_properties()
        inner_volume_specs = [
            # 添加SharedMemoryVolume
            VolumeSpecCreate(file_set=f"/dev/shm", mount_path="/dev/shm", volume_type="SHARED_MEMORY",
                             quota=math.ceil(self.replica_specs.resource.memory.value / 2)),
            # 添加初始化盘
            VolumeSpecCreate(file_set="notebook-init-copy", mount_path="/aicp-init", volume_type="EMPTY_DIR",
                             quota=1),
        ]
        if app.settings.MODEL_MOUNT_ENABLE:
            # 添加宿主机模型路径挂载
            # 添加宿主机模型名称
            inner_volume_specs.append(
                VolumeSpecCreate(
                    file_set=app.settings.POD_MODEL_NAME_MOUNT_PATH,
                    mount_path=app.settings.HOST_MODEL_NAME_MOUNT_PATH,
                    volume_type="HOST_PATH",
                    permission='ro'
                ))
        # 添加数据盘
        if app.settings.LOCAL_STORAGE_ENABLE and int(self.replica_specs.custom_data_disk_size) > 0:
            inner_volume_specs.append(
                VolumeSpecCreate(file_set=self.uuid, mount_path="/root/aicp-data", volume_type="LOCAL",
                                 quota=self.replica_specs.custom_data_disk_size)
            )

        # 添加公共NFS
        if app.settings.PUBLIC_NFS_SERVER:
            inner_volume_specs.append(
                VolumeSpecCreate(file_set=self.uuid, mount_path=app.settings.PUBLIC_NFS_IN_POD_PATH,
                                 volume_type="PUBLIC_NFS")
            )

        # HYGON(DCU) 资源添加 /opt/hyhal
        if self.get_gpu_vendor() == "HYGON":
            inner_volume_specs.append(
                VolumeSpecCreate(file_set="/opt/hyhal", mount_path="/opt/hyhal", volume_type="HOST_PATH", quota=1,
                                 permission='ro')
            )

        # 添加本地盘
        for paths in app.settings.LOCAL_PATH_STORAGE_MOUNT_PATHS:
            # 兼容LOCAL_PATH_STORAGE_MOUNT_PATHS为list和dict两种格式
            permission = "rw"
            mount_path = paths
            if isinstance(app.settings.LOCAL_PATH_STORAGE_MOUNT_PATHS, dict):
                mount_path = app.settings.LOCAL_PATH_STORAGE_MOUNT_PATHS[paths]
            elif isinstance(app.settings.LOCAL_PATH_STORAGE_MOUNT_PATHS, list):
                if isinstance(paths, LocalPathStorageMount):
                    mount_path = paths.MOUNT_PATH
                    permission = paths.PERMISSION
                    paths = paths.HOST_PATH

            inner_volume_specs.append(
                VolumeSpecCreate(file_set=paths, mount_path=mount_path, volume_type="LOCAL_PATH", permission=permission)
            )
        for volume_spec in volume_specs_create + inner_volume_specs:
            vm = VolumesManager(user, self.namespace, volume_spec)
            try:
                vm.create_storage()
                vs: NotebookVolumeSpec = NotebookVolumeSpec(**volume_spec.dict())
                if vm.volume_spec.volume_type.upper() == "GPFS":
                    vm.volume_spec.quota = vs.quota = int(vm.volume.quota[:-2])
                self.volume_specs.append(vs)
                vs.pvc_name = vm.volume.pvc_name
            except Exception as e:
                logger.exception(f"create volume failed : {e}")
                logger.error(f"create volume failed : {e}")
                # vm.delete_storage()
                raise VolumeCreateException(f"{volume_spec.volume_type}: {volume_spec.file_set}")

    def check_volume_specs_exists(self, user):
        for volume_spec in self.volume_specs:
            vm = VolumesManager(user, self.namespace, volume_spec)
            if not vm.exists():
                return False
        return True

    @property
    def billing_enable(self):
        """

        :return:
        """
        return app.settings.billing_enable and self.replica_specs.rg_id is None \
            and self.replica_specs.specs and self.replica_specs.specs.startswith("sku_")

    def get_billing_price_info(self) -> Dict:
        """
        generate billing price info
        :return:
        """
        self.replica_specs.resource.init_properties()
        return self.replica_specs.resource.get_billing_price_info()

    def get_price(self, charge_mode: str, duration: int):
        """
        get price
        :param charge_mode:
        :param duration:
        :param user_id:
        """
        if self.billing_enable:
            return QAIBillingService().get_price(self.user_id, self.get_billing_price_info(),
                                                 charge_mode=charge_mode, duration=duration)
        return None

    def lease(self):
        """
        lease resource
        """
        if self.billing_enable:
            QAIBillingService().lease(self.uuid, self.user_id, self.get_billing_price_info(),
                                      charge_mode=self.charge_mode, duration=self.duration, check_resource_balance=True,
                                      auto_renew=self.auto_renew, next_charge_mode=self.next_charge_mode)

    def unlease(self):
        """

        :param resource_id:
        :param user_id:
        """
        if self.billing_enable:
            logger.info(f"unlease: {self.uuid}")
            try:
                QAIBillingService().unlease(self.uuid, self.user_id)
            except BillingServiceException as e:
                logger.error(f"unlease error: {e}")
                if e.billing_code == 2100:
                    return
                raise e

    async def async_unlease(self):
        """

        :param resource_id:
        :param user_id:
        """
        if self.billing_enable:
            logger.info(f"async_unlease: {self.uuid}")
            try:
                await AsyncQAIBillingService().unlease(self.uuid, self.user_id)
            except BillingServiceException as e:
                logger.error(f"unlease error: {e}")
                if e.billing_code == 2100:
                    return
                raise e

    def suspend_billing(self):
        """
        suspend
        """
        if self.billing_enable:
            QAIBillingService().suspend(self.uuid, self.user_id)

    def restart_billing(self, new_price_info=None):
        """
        restart
        """
        if self.billing_enable:
            QAIBillingService().restart(self.uuid, self.user_id, new_price_info)

    async def async_restart_billing(self, new_price_info=None, recover=False):
        """
        restart
        """
        if self.billing_enable:
            return await AsyncQAIBillingService().restart(self.uuid, self.user_id, new_price_info, recover=recover)

    async def async_suspend_billing(self):
        """
        restart
        """
        if self.billing_enable:
            await AsyncQAIBillingService().suspend(self.uuid, self.user_id)

    def update_billing(self, new_price_info, count):
        """
        update
        """
        if self.billing_enable:
            QAIBillingService().restart(self.uuid, self.user_id, new_price_info, count)

    def is_resource_group(self):
        return self.replica_specs.rg_id is not None

    def check_resource_balance(self, new_price_info=None):
        """
        check resource balance, when start notebook
        :return:
        """
        if self.billing_enable:
            new_price_info = new_price_info or self.get_billing_price_info()
            new_price_info["replicas"] = 1
            return QAIBillingService().check_resource_balance(self.uuid, self.user_id, new_price_info)
        return True

    def update_lease(self, price_info, count):
        """
        update lease
        """
        if self.billing_enable:
            QAIBillingService().update_lease(self.uuid, self.user_id, price_info, count)

    __tablename__ = "notebook"


class NotebookRead(Notebook, UserPatchInfoModel, table=False):
    ws_url: str = Field(None, nullable=True, description="Websocket地址")
    notebook_volume_specs: Optional[List[NotebookVolumeSpec]] = Field([], description="挂载配置列表", sa_type=JSON,
                                                                      alias="volume_specs")
    notebook_replica_specs: Optional[NotebookReplicaSpec] = Field(..., description="", sa_type=JSON,
                                                                  alias="replica_specs")
    servers: Optional[List[AicpServers]] = Field([], nullable=True, description="服务地址列表", sa_type=JSON, )
    pod_ips: Optional[List] = Field(None, nullable=True, description="pod ip列表", sa_type=JSON, )
    running_time: Optional[timedelta] = Field(None, nullable=True, description="运行时间")

    @validator('ws_url')
    def make_ws_url(cls, v: str, values: dict) -> str:
        return f"/aicp/kapi/v1/ws/namespaces/{values['namespace']}/pods/{values['uuid']}-0/container/{values['uuid']}/exec"

    @validator('running_time')
    def make_running_time(cls, v: str, values: dict) -> timedelta:
        if values['status'] == NotebookStatus.Terminated:
            return values['updated_at'] - values['created_at']

        return datetime.now() - values['created_at']

    class Config:
        schema_extra = {"example": "ex_hero_read"}


class NotebookCreate(NotebookBase, UserIdModel, table=False):
    replica_specs: ReplicaSpecCreate = Field(..., description="副本配置列表")
    volume_specs: Optional[List[VolumeSpecCreate]] = Field([], description="挂载配置列表")
    custom_image_secret: Optional[CustomImageSecret] = Field(CustomImageSecret(has_auth=False),
                                                             description="自定义镜像仓库认证信息")
    # pip源 pypi aliyun tsinghua huaweicloud
    pip: str = Field("pip源", nullable=True, description="pip源")
    # conda源 conda aliyun tsinghua
    conda: str = Field("conda源", nullable=True, description="conda源")
    # apt源 ubuntu aliyun tsinghua huaweicloud
    apt: str = Field("apt源", nullable=True, description="apt源")
    # http https
    custom_first: str = Field("http", nullable=True, description="9001协议")
    custom_second: str = Field("http", nullable=True, description="9002协议")
    command: Optional[str] = Field(None, nullable=True, description="自定义启动命令")

    class Config:
        schema_extra = {
            "example": {
                "volume_specs": [{"volume_type": "GPFS", "file_set": "ddeddfgsfveafewsf", "mount_path": "/root/epfs"}],
                "envs": [],
                "image_type": "official",
                "image": "public/tverous:pytorch-notebook",
                "replica_specs": {"replicas": 1, "replica_type": "Worker", "specs": "sku_7Q5Q7yNLJZoA"},
                "service": "qai", "namespace": "usr-wj2gdxug", "action": "notebooks/namespaces/usr-wj2gdxug/create",
                "zone": "jinan1", "owner": "usr-WJ2gdXUg", "user_id": "usr-WJ2gdXUg"},
            "pip": "pypi", "conda": "conda", "apt": "ubuntu"
        }


class NotebookSearchQueryParams(CommonSearchQueryParams):
    """
    开发机搜索参数
    """

    def __init__(
            self,
            offset: int = 0,
            limit: int = 10,
            name: str = Query(default=None, description="实例名字, 模糊搜索"),
            reverse: bool = Query(default=True, description="按时间倒序排列"),
            order_by: str = Query(default="created_at", description="排序字段"),
            status: Optional[List[str]] = Query(default=None, description="运行状态"),
            endpoints: Optional[List[str]] = Query(default=None, description="作业类型"),
            start_at: Optional[datetime] = Query(default=None, description="最早创建时间"),
            end_at: Optional[datetime] = Query(default=None, description="最晚创建时间"),
            owner: Optional[str] = Query(default=None, description="拥有者"),
    ):
        super().__init__(offset, limit)
        self.name = name
        self.offset = offset
        self.limit = limit
        if status and "Paused" in status:
            status.remove("Paused")
            status.append("Suspended")
        self.status = status
        self.order_by = order_by
        self.reverse = reverse
        self.endpoints = endpoints
        self.start_at = start_at
        self.end_at = end_at
        self.owner = owner


class NotebookSaveToImage(BaseModel):
    nb_id: str
    image_name: str
    image_tag: str = "latest"
    describe: str = ''

    @property
    def biz_id_field(self):
        return "nb_id"


class NotebookUpdateName(BaseModel):
    notebook_name: str
    uuid: str


class NoteBookUuid(BaseModel):
    uuid: str


class NoteBookUuids(BaseModel):
    uuids: List[str]


class NoteBookStopParam(NoteBookUuids):
    save_image: bool = True

class NoteBookPreCheckParam(NoteBookUuid):
    sku_id: Optional[str] = Field(None, description="更改的sku_id")

class NoteBookUuidSetStopTimeParam(BaseModel):
    uuid: str
    stop_time: Optional[datetime] = Field(None, description="停止时间")


class NoteBookUuidDeleteParam(BaseModel):
    uuid: str
    auto_delete_time: Optional[datetime] = Field(None, description="停止时间")


class NoteBookStartParam(BaseModel):
    uuids: List[str] = Field(..., description="开发机UUID列表, 仅支持一个", min_items=1, max_items=1)
    sku_id: Optional[str] = Field(None, description="更改的sku_id")
    disable_mount: Optional[bool] = Field(False, description="是否禁用自定义存储挂载")
    volume_specs: Optional[List[VolumeSpecCreate]] = Field([], description="存储挂载配置列表")
    custom_spec: Optional[ReplicaSpecCreate] = Field(None,
                                                     description="自定义规格, 仅限于资源组的notebook可以使用该字段")


class NotebookUpdateSource(BaseModel):
    uuid: str
    source_key: str
    source_val: str


class NoteBookServerParam(BaseModel):
    services: List[str] = Field(..., description="服务类型列表", include=["jupyter", "vscode", "ssh", "custom"])


class NoteBookPortParam(BaseModel):
    uuid: str
    port: int = Field(..., description="开放端口号", gt=0, lt=65536)
    protocol: str = Field("http", description="地址协议")


class NoteBookMountsParam(NoteBookUuid, VolumeSpecCreate):
    ...


class NoteBookChangeResourceGroupParam(NoteBookUuid):
    rg_id: str = Field(..., description="资源组ID")
    rg_node_id: str = Field(None, description="资源组节点ID, 如果为None则随即调度")


class NoteBookChangeImageParam(NoteBookUuid, ImageModel):
    secret: CustomImageSecret = Field(CustomImageSecret(has_auth=False), description="自定义镜像仓库认证信息")


class NotebookServices(ActiveRecordMixin, table=True):
    __tablename__ = "notebook_services"
    uuid: str = Field(
        ...,
        primary_key=True,
        index=True,
        nullable=False,
    )
    server: str = Field(..., nullable=False)

class NotebookStartPrecheckResult(BaseModel):
    """
    启动前检查结果
    """
    can_start: bool = Field(..., description="剩余量是否可以启动")
    can_start_on_pre_node: bool = Field(..., description="是否可以启动在前置节点")
