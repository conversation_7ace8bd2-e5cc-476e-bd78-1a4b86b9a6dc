# cython: skip
from enum import Enum
from typing import Optional

from pydantic import BaseModel
from sqlmodel import Field, SQLModel

from app.core.models import TimestampModel, StatusModel, ActiveRecordMixin, UserIdModel


class BmInstanceBase(SQLModel, table=False):
    bm_node_id: str = Field(primary_key=True)
    hostname: Optional[str] = Field('', nullable=True, description="节点hostname")
    name: Optional[str] = Field('', description="节点名称")
    cpu: int = Field(description="节点cpu")
    memory: int = Field(description="节点内存")
    cpu_model: str = Field('', description="cpu型号")
    gpu: int = Field(0, description="节点gpu")
    gpu_memory: int = Field(0, description="节点显存")
    gpu_model: str = Field('', description="gpu型号")
    gpu_name: Optional[str] = Field("", nullable=True, description="gpu名称, 实际显示在前端页面的")
    nvlink: bool = Field('', description="是否支持nvlink")
    network: Optional[int] = Field(0, description="网络带宽")
    os_disk: Optional[int] = Field(nullable=True, description="系统盘数容量")
    disk: Optional[int] = Field(0, description="数据盘容量")
    node_type: Optional[str] = Field('', description="节点类型")
    bm_type: Optional[str] = Field('', description="bm类型  bm1或是bm3带dpu")
    during: Optional[int] = Field(nullable=True, description="包年包月时长")
    auto_renewal: Optional[int] = Field(nullable=True, description="是否自动续约")
    next_charge_mode: Optional[str] = Field(nullable=True, description="到期后计费模式")
    sku_id: Optional[str] = Field(nullable=True, description="产品类型id")
    billing_order_id: Optional[str] = Field("", nullable=True, description="计费订单id")
    username: Optional[str] = Field('', description="登录用户名")
    password: Optional[str] = Field('', description="密码")


class BmInstance(BmInstanceBase, TimestampModel, StatusModel, UserIdModel, ActiveRecordMixin, table=True):
    __tablename__ = "bm_instance"


class BmImageBase(SQLModel, table=False):
    bm_image_id: str = Field(primary_key=True)
    arch: Optional[str] = Field(description="x86 or arm")
    os_family: Optional[str] = Field(description="操作系统类别")
    base_image: Optional[str] = Field(description="操作系统基础镜像")
    root_image: Optional[str] = Field(description="操作系统根镜像")
    format: Optional[str] = Field(description="格式")
    boot_mode: Optional[str] = Field(description="启动模式")
    storage_type: Optional[str] = Field(description="存储")
    path: Optional[str] = Field(description="镜像路径")
    root_partition_fs:Optional[str] = Field(description="ext4")
    checksum: Optional[str] = Field(description="校验码")
    checksum_type: Optional[str] = Field(description="校验算法")

class BmImage(BmImageBase, TimestampModel, StatusModel, UserIdModel, ActiveRecordMixin, table=True):
    __tablename__ = "bm_image"


class BmStatus(str, Enum):
    running = "running"
    stopped = "stopped"
    deleted = "deleted"

class CreateBmInstanceRequest(BaseModel):
    name: str
    sku_id: str
    count: int = 1
    duration: int = 1
    auto_renew: int = 1
    charge_mode: str = "monthly"
    next_charge_mode: str = "monthly"
    username: Optional[str] = None
    password: Optional[str] = None


class UpdateBmInstanceRequest(BaseModel):
    bm_node_id: str
    name: str
    status: BmStatus


class CreateBmInstanceResponse(CreateBmInstanceRequest):
    bm_node_id: str




class CreateBmImageRequest(BaseModel):
    arch: Optional[str] ="x86_64"
    os_family: Optional[str] = "ubuntu"
    base_image: Optional[str] = "focal1uefix64"
    root_image: Optional[str] = "focal1uefix64"
    format: Optional[str] = "qcow2"
    boot_mode: Optional[str] = "UEFI"
    storage_type: Optional[str] = ""
    path: Optional[str] = ""
    root_partition_fs = "ext4"
    checksum: Optional[str] = ""
    checksum_type: Optional[str] = "md5"
    user_id: str = "system"
    root_user_id: str = "system"



class BmStatus(str, Enum):
    pending = "pending"
    preparing = "preparing"
    running = "running"