"""
cleanup those resources that are not in use by day end
1. unused openebs-zfs local
2. unused aicp-oss-pvc
3. unused aicp-gpfs-pvc

4. user that more than 15 days not login
"""
import os
import re
import sys
from collections import defaultdict
import asyncio
from typing import Optional, Type, Union

from kubernetes.client import ApiException


sys.path.append("/code")

from sqlmodel import select
from app.apps.trains import Train
import datetime
from datetime import datetime as file_datetime
from datetime import timedelta
from contextlib import asynccontextmanager

import kr8s
from kr8s import NotFoundError
from kr8s.asyncio.objects import PersistentVolumeClaim
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker
from kr8s.objects import Deployment, Job, Pod, StatefulSet

import app
from app import logger
from app.apps.notebooks.kr8s_objects.notebook import NoteBook
from app.apps.trains.kr8s_objects import ALL_TRAINS
from app.core.kube.api.kfam import KfamClient
from app.models.operation_record import OperationRecord
from app.models.user import UserInfo, UserStatusEnum
from app.core.kube.api import ALL_STATUS_PHASES, STATUS_PHASE
from app.jobs.objects.async_objects import NoteBookObj
from app.models.notebooks import Notebook
from app.apps.notebooks.kr8s_objects.imagebuilder import ImageBuilder
from app.core.kube.api.custom_resource import get_cluster_custom_rsrc, delete_cluster_custom_rsrc, remove_cluster_custom_finalizer


END_STATUS = [STATUS_PHASE.Succeeded, STATUS_PHASE.Failed, STATUS_PHASE.Terminating, STATUS_PHASE.Terminated,
              STATUS_PHASE.Completed, STATUS_PHASE.Deleted, STATUS_PHASE.CreateFailed]

RUNNING_STATUS = list(set(ALL_STATUS_PHASES) - set(END_STATUS))

async_engine = create_async_engine(
    app.settings.DB_ASYNC_CONNECTION_STR,
    echo=app.settings.ECHO,
    future=True,
    pool_size=10,
    max_overflow=20,
    pool_recycle=3600,
    pool_pre_ping=True,
)


@asynccontextmanager
async def get_async_session() -> AsyncSession:
    async_session = sessionmaker(bind=async_engine, class_=AsyncSession, expire_on_commit=False)
    async with async_session() as session:
        yield session
        await session.commit()


async def get_notebook_by_uuid(uuid: str) -> Optional[Type[Notebook]]:
    async with get_async_session() as session:
        return await session.get(Notebook, uuid)


async def get_train_by_uuid(uuid: str) -> Optional[Type[Train]]:
    async with get_async_session() as session:
        return await session.get(Train, uuid)


async def get_train_by_code_path_uuid(code_path_uuid: str) -> Optional[Type[Train]]:
    async with get_async_session() as session:
        stmt = select(Train).where(Train.code_path_uuid == code_path_uuid)
        res = await session.execute(stmt)
        row = res.one_or_none()
        if row is not None:
            return row[0]


async def get_instance_by_name(object_name: str) -> Optional[Union[Notebook, Train]]:
    if object_name.startswith("nb-"):
        return await get_notebook_by_uuid(object_name)
    elif object_name.startswith("tn-"):
        return await get_train_by_uuid(object_name)
    elif len(object_name) == 36 and object_name.count("-") == 4:
        return await get_train_by_code_path_uuid(object_name)
    return None


async def cleanup_notebook_local_pvc(pvc):
    """
    clean up local pvc that are not in use
    :param pvc:
    :return:
    """
    # pvc.metadata.creationTimestamp : str = '2024-04-22T15:35:40Z'
    # if now - pvc.metadata.creationTimestamp > 7 days, then try to delete pvc
    if datetime.datetime.now() - datetime.datetime.strptime(
            pvc.metadata.creationTimestamp, "%Y-%m-%dT%H:%M:%SZ") < datetime.timedelta(days=app.settings.PVC_RETENTION_DAYS):
        logger.info(f"pvc {pvc.name} is created less than 7 days, skip.")
        return

    logger.info(f"pvc {pvc.name} is created more than 7 days, try to delete it.")
    # if pvc is used by notebook, do not delete
    if not pvc.name.startswith(app.settings.LOCAL_STORAGE_CLASS):
        logger.error(f"pvc {pvc.name} is not a local pvc, skip.")
        return
    notebook_name = pvc.name[len(f"{app.settings.LOCAL_STORAGE_CLASS}-"):]
    if not notebook_name.startswith("nb-"):
        logger.error(f"pvc {pvc.name} is not a notebook pvc, skip.")
        return

    async with get_async_session() as session:
        notebook = await session.get(Notebook, notebook_name)
        if notebook is None:
            logger.info(f"notebook {notebook_name} not found, skip.")
            return
        if notebook.status in END_STATUS and (datetime.datetime.now() - notebook.updated_at) < datetime.timedelta(
                days=app.settings.PVC_RETENTION_DAYS):
            logger.info(f"notebook {notebook_name} is in end status and updated less than 7 days, skip.")
            return

    try:
        # if notebook exists, do not delete pvc
        await NoteBookObj.get(notebook_name, namespace=pvc.namespace)
        logger.info(f"notebook {notebook_name} exists, skip.")
        return
    except NotFoundError as e:
        logger.info(f"notebook {notebook_name} not found, delete pvc {pvc.name}")
        await pvc.delete()
        logger.info(f"pvc {pvc.name} is deleted successfully.")
    except Exception as e:
        logger.error(f"error when get notebook {notebook_name}, {e}")


async def cleanup_gpfs_pvc(pvc):
    pass


async def cleanup_pvc():
    """
    clean up local pvc that are not in use
    """
    for pvc in await PersistentVolumeClaim.list(namespace=kr8s.ALL):
        try:
            if pvc.spec.storageClassName == app.settings.LOCAL_STORAGE_CLASS:
                await cleanup_notebook_local_pvc(pvc)
            elif pvc.spec.storageClassName.startswith("qingcloud-gpfs-storage-class"):
                await cleanup_gpfs_pvc(pvc)
            else:
                logger.info(f"pvc {pvc.name} is not need to be cleaned up. skip it.")
        except Exception as e:
            logger.exception(f"error when cleanup pvc {pvc.name}, {e}")


def check_user_resources(user: UserInfo) -> bool:
    """
    check if user has resources
    :param user:
    :return:
    """
    # 检查用户是否存在运行中的资源
    resources_type = [NoteBook] + ALL_TRAINS
    for resource_type in resources_type:
        resources = kr8s.get(resource_type.endpoint, namespace=user.namespace)
        if resources:  # noqa
            logger.info(f"user {user.user_id} has resources {resource_type.kind}, skip.")
            return True

    # 查找用户inf资源 inf属于deployment
    if list(filter(lambda x: x, Deployment.list(namespace=user.namespace))): # noqa
        logger.info(f"user {user.user_id} has resources Deployment, skip.")
        return True
    if list(filter(lambda x: x, StatefulSet.list(namespace=user.namespace))): # noqa
        logger.info(f"user {user.user_id} has resources StatefulSet, skip.")
        return True
    if list(filter(lambda x: x, Pod.list(namespace=user.namespace))): # noqa
        logger.info(f"user {user.user_id} has resources Pod, skip.")
        return True
    if list(filter(lambda x: x, Job.list(namespace=user.namespace))): # noqa
        logger.info(f"user {user.user_id} has resources Job, skip.")
        return True

    logger.info(f"user {user.user_id} has no <notebook|train|inf> resources.")
    return False


async def cleanup_user():
    """
    clean up user should be followed the rules:
    1. more than 15 days not login
    2. Not has resources, including notebook, train, inf
    3. Not has active sub user.

    """
    logger.info(f"try to cleanup user that more than {app.settings.PROFILE_RETENTION_DAYS} days not login.")
    async with get_async_session() as session:
        stmt = select(UserInfo) \
            .where(
            UserInfo.last_login_at < datetime.datetime.now() - datetime.timedelta(days=app.settings.PROFILE_RETENTION_DAYS),
            UserInfo.status.in_([UserStatusEnum.active, UserStatusEnum.warning])
        )
        res = await session.execute(stmt)
        users = res.scalars().all()
    total = len(users)
    index = 0

    group = 'kubeflow.org'
    version = 'v1'
    plural = 'profiles'

    for user in users:
        # 检查用户是否有资源节点
        # too long time for process other resource
        index += 1
        logger.info(f"cleanup user {index}/{total}")
        user = UserInfo.one_by_id(user.user_id)
        if not (user.last_login_at < datetime.datetime.now() - datetime.timedelta(days=app.settings.PROFILE_RETENTION_DAYS)):
            logger.info(f"usr[{user.user_id}] relogined, skip.")
            continue
        logger.info(f"usr[{user.user_id}] last login at {user.last_login_at}, try to clean it.")

        # 获取用户的所有资源
        if check_user_resources(user):
            logger.info(f"user {user.user_id} has resources, skip.")
            user.last_login_at = datetime.datetime.now()
            continue
        try:
            # 清理namespace
            try:
                profile = get_cluster_custom_rsrc(group, version, plural, user.namespace)
            except ApiException as e:
                if e.status == 404:
                    logger.info(f"user {user.user_id} has no profile, skip.")
                    profile = None
                else:
                    raise e
            if profile:
                logger.info(f"user {user.user_id} has profile, delete it.")
                # profiles has nothing finalizer
                remove_cluster_custom_finalizer(group, version, plural, user.namespace)
                delete_cluster_custom_rsrc(group, version, plural, user.namespace)

            user.status = UserStatusEnum.inactive
            user.save()
            logger.info("Inactive user successfully.")
            OperationRecord.create_by_user_info(user, reason="Auto Cleanup User").insert()

        except Exception as e:
            logger.exception(f"error when cleanup user {user.user_id}, {e}")


async def cleanup_image_tar():
    """
    清理被删除15天后的镜像压缩包文件
    """
    try:
        logger.info("Try to cleanup 'Tape Archive' that notebook has delete over 15 days.")
        unique_uuids_set = set()
        # 获取当前时间
        now = file_datetime.now()
        # 用于存储超过15天的文件
        expired_files_map = defaultdict(list)
        # 获取文件夹下unique_uuids
        for filename in os.listdir(app.settings.IMAGE_DUMP_PATH):
            # 命名为文件名:14位时间戳
            match = re.match(r"(.+):(\d{14})\.tar$", filename)
            if match:
                uuid = match.group(1)
                file_path = os.path.join(app.settings.IMAGE_DUMP_PATH, filename)
                expired_files_map[uuid].append(file_path)
                unique_uuids_set.add(uuid)

        # just keep 2 files for each notebook
        for notebook_id, files in expired_files_map.items():
            logger.info(f"Notebook {notebook_id} has {len(files)} expired files.")
            if len(files) > 2:
                logger.info(f"Notebook {notebook_id} has more than 2 expired files, just keep 2 latest files.")
                files.sort(key=os.path.getctime, reverse=True)
                for file_path in files[2:]:
                    try:
                        os.remove(file_path)
                        logger.info(f"Delete file: {file_path}")
                    except Exception as e:
                        logger.error(f"删除文件失败: {file_path}, error: {e}")
                expired_files_map[notebook_id] = files[:2]

        # 获取数据库下与文件夹内对应的Notebooks
        async with get_async_session() as session:
            stmt = select(Notebook).where(Notebook.status == "Terminated",
                                          Notebook.uuid.in_(list(unique_uuids_set)),
                                          Notebook.updated_at <= now - datetime.timedelta(days=app.settings.IMAGE_DUMP_DELETE_DAYS))
            res = await session.execute(stmt)
            notebooks = res.scalars()
            for notebook in notebooks:
                if notebook.uuid in expired_files_map:
                    for tar_file_path in expired_files_map[notebook.uuid]:
                        # Check file again.
                        if os.path.exists(tar_file_path):
                            try:
                                os.remove(tar_file_path)
                                logger.info(f"删除文件: {tar_file_path}")
                            except Exception as e:
                                logger.error(f"删除文件失败: {tar_file_path}, error: {e}")
                        else:
                            logger.warning(f"文件不存在: {tar_file_path}")
    except Exception as e:
        logger.exception("cleanup_image_tar error: %s", e)

async def cleanup_imagebuilder():
    imagebuilders = ImageBuilder.list(namespace=kr8s.ALL)
    for imagebuilder in imagebuilders:
        try:
            if datetime.datetime.now() - imagebuilder.create_at > datetime.timedelta(days=1) and not imagebuilder.is_runnnig:
                logger.info(f"Clean imagebuilder {imagebuilder.name}")
                imagebuilder.delete()
        except Exception as e:
            logger.exception(f"Clean imagebuilder {imagebuilder.name} error: {e}")


async def main():
    await cleanup_pvc()
    await cleanup_user()
    await cleanup_image_tar()
    await cleanup_imagebuilder()


if __name__ == '__main__':
    asyncio.run(main())
