import json
import sys
import time
import traceback

import asyncssh

from app.core.db import get_db_session_local

sys.path.append("/code")
from app.core.rlock import RedisClient
from app.cruds.resource_group import ResourceGroupCrud
import asyncio
import threading
import datetime
from typing import Dict, List, Union
from asyncio import Lock
import math
import re
from contextlib import asynccontextmanager
from zoneinfo import ZoneInfo

from kubernetes_asyncio import client, config, watch
from kubernetes_asyncio.client import ApiEx<PERSON>, V1NodeCondition, V1Taint
from kubernetes_asyncio.config import ConfigException
import kr8s
from kr8s.asyncio.objects import Node, APIObject, Pod, Event
from kr8s import NotFoundError

from opensearchpy import AsyncOpenSearch
from opensearchpy.helpers import async_bulk
from sqlalchemy.ext.asyncio import create_async_engine
from sqlalchemy import AsyncAdaptedQueuePool, update
from sqlalchemy import <PERSON>alar<PERSON><PERSON>ult
from sqlalchemy.orm import sessionmaker, selectinload
from sqlmodel import and_, or_, select
from sqlmodel.ext.asyncio.session import AsyncSession

import app
from app import watch_config_file
from app.core.qingcloud.common import get_qingcloud_user_info_by_user_id
from app.core.utils import TransferImageStatus, TransferImageTaskManager, get_node_ip_and_runtime, gpu_manager, \
    generate_random_string
from app.models.gpu import GpuStaticInfo, NodeStaticInfo, IbDevInfo
from app.core.constant import NOTEBOOK_PRE_IMAGE_INFO_KEY, NVIDIA, HEXAFLAKE, HUAWEI, HYGON, STATUS_SUSPENDED, \
    RG_NODE_TAG_KEY, RG_NODE_TOLERATION_KEY_SUSPENDED
from app.core.ufm.models import UFMPkeys
from app.core.ufm.ufm_api import async_bind_ufm_pkey_guids, async_add_pkey_request, async_unbind_ufm_pkey_guid
from app.core.opensearch.client import async_es_client
from app.core.loggers import async_pool_logger as logger
from app.core.async_pool import AsyncPool
from app.core.qingcloud.billing import QAIBillingService
from app.core.qingcloud.interface import send_message_request, INSTANCE_SHUTDOWN, ZONE_INFO, INSTANCE_RELEASE, \
    AICP_JOB_STATUS, AICP_JOB_RESTART
from app.models.trains import Train, TrainStatus
from app.apps.trains.kr8s_objects import ALL_ENDPOINTS, ALL_TRAINS
from app.models.notebooks import DeletedStatus, Notebook, NotebookReason, NotebookStatus, SearchableNotebookStatus
from app.apps.notebooks.background_task import stop_notebooks_in_background
from app.models.operation_record import OperationRecord, NotebookMessageStopAction, NotebookMessageReleaseAction, \
    TrainMessageStatusAction
from app.core.kube.api import EVENT_TYPE_WARNING, STATUS_PHASE
from app.models.resource_group import ResourceNode, ResourceNodeStatus
from app.core.prometheus.client import PromethusAsyncClient
from app.jobs.objects.async_objects import NoteBookObj, PyTorchJob  # noqa
from app.apps.notebooks.kr8s_objects.notebook import StatusPhase, async_get_notebook_status
from app.core.volumes.local_storage import LocalStorageVolume

DEFAULT_TZINFO = ZoneInfo("Asia/Shanghai")

kube_resource_index_name = "kube_resource"
log_index_name = f"{app.settings.cluster_name}-logs*"
auth = (app.settings.OPENSEARCH_USER, app.settings.OPENSEARCH_PASSWORD)
opensearch_client = AsyncOpenSearch(
    hosts=[app.settings.OPENSEARCH_HOST],
    http_auth=auth,
    verify_certs=False,
)


class LockManager:
    def __init__(self):
        # 存储每个ID对应的锁
        self.locks = {}
        self.latest_lock_time = {}
        self.expire_time = 60 * 60 * 24

    def get_lock(self, obj: Dict) -> Lock:
        # 获取ID对应的锁，如果不存在则创建一个新的锁
        self.latest_lock_time[obj['metadata']['name']] = time.time()
        if obj['metadata']['name'] not in self.locks:
            self.locks[obj['metadata']['name']] = asyncio.Lock()
        return self.locks[obj['metadata']['name']]

    def del_lock(self, obj: Dict):
        # 删除ID对应的锁
        if obj['metadata']['name'] in self.locks:
            del self.locks[obj['metadata']['name']]

    def expire_lock(self, obj: Dict):
        # 删除ID对应的锁
        if obj['metadata']['name'] in self.locks:
            if time.time() - self.latest_lock_time[obj['metadata']['name']] > self.expire_time:
                del self.locks[obj['metadata']['name']]
        if obj['metadata']['name'] in self.latest_lock_time:
            del self.latest_lock_time[obj['metadata']['name']]

    async def clean_expired_lock(self):
        """
        定时清理过期的锁
        """
        while True:
            for k in list(self.locks.keys()):
                if time.time() - self.latest_lock_time[k] > self.expire_time:
                    del self.locks[k]
            await asyncio.sleep(60)


class InstanceVersions:

    def __init__(self):
        self.version_map = {}

    def get_max_version(self, obj: Dict):
        """
        get max version
        :param obj: a k8s resource dict
        :return:
        """
        return self.version_map.get(obj['metadata']['name'], 0)

    def delete(self, obj: Dict):
        """
        delete instance version
        :param instance_id:
        """
        if obj['metadata']['name'] in self.version_map:
            del self.version_map[obj['metadata']['name']]

    def is_latest_version(self, obj: Dict):
        """
        is the latest version
        :param obj: a k8s resource dict
        :param version:
        :return:
        """
        if obj['metadata']['name'] not in self.version_map or \
                int(obj['metadata']['resourceVersion']) >= self.version_map[obj['metadata']['name']]:
            self.version_map[obj['metadata']['name']] = int(obj['metadata']['resourceVersion'])
            return True
        return False

    def update_latest_version(self, obj: Dict):
        """
        update latest version
        :param obj: a k8s resource dict
        :param version:
        """
        if self.is_latest_version(obj):
            self.version_map[obj['metadata']['name']] = int(obj['metadata']['resourceVersion'])


instance_versions = InstanceVersions()

lock_manager = LockManager()

async_engine = create_async_engine(
    app.settings.DB_ASYNC_CONNECTION_STR,
    echo=app.settings.ECHO,
    future=True,
    pool_size=20,
    max_overflow=30,
    pool_recycle=3600,
    pool_pre_ping=True,
    poolclass=AsyncAdaptedQueuePool,
)

async_pool = AsyncPool(maxsize=5000)


def safe_index(lst, index):
    try:
        return int(lst[index])
    except Exception as e:
        return 0


async def run_command(host, command, timeout=10):
    key = '/root/.ssh/id_rsa'
    async with asyncssh.connect(host=host, username='root', client_keys=[key], known_hosts=None) as conn:
        try:
            logger.debug("echo cmd[%s]", command)
            result = await conn.run(command, check=True, timeout=timeout)
            logger.debug("cmd result [%s]", result.stdout)
            return result.exit_status, result.stdout
        except Exception as e:
            logger.info("check node type")
            return -1, ''


async def check_nvlink_enable(host):
    status_code, output = await run_command(host, "lspci | grep -i NVIDIA | grep Bridge")
    if output.strip():
        return True
    return False


async def check_gpu_type(host):
    dev_types = [
        {"cmd": "nvidia-smi", "product": "nvidia"},
        {"cmd": "hxsmi list", "product": "hfk"},
        {"cmd": "npu-smi info", "product": "huawei"},
        {"cmd": "source /usr/local/hygon/env.sh; hy-smi", "product": "hygon"}
        # ln -s  /usr/local/hygon/dtk-24.04/env.sh  /usr/local/hygon/env.sh
    ]
    for dev_type in dev_types:
        status_code, output = await run_command(host, dev_type["cmd"])
        logger.info("status_code [%s]", status_code)
        if status_code == 0:
            logger.info(f"{host} is {dev_type['product']} node")
            return dev_type["product"]
    logger.info(f"{host} is cpu node")
    return "cpu"


async def check_cpu_type(host):
    status_code, output = await run_command(host, "lscpu | grep 'Model name'")
    if status_code == 0:
        try:
            if output:
                cpu_model = output.split(":")[1].strip()
            else:
                cmd = r"dmidecode -t processor | grep Version | awk -F ':' '{print $2}' | head -n 1"
                status_code, output = await run_command(host, cmd)
                if output:
                    return output
                else:
                    return "not found"
            return cpu_model
        except Exception as e:
            logger.info("get cpu model failed")
    return 'not found'


async def get_host_docker_overlay2_size(host):
    """
    default is 0, not limit
    :param host:
    :return:
    """
    cmd = """grep '"storage-opts"' /etc/docker/daemon.json | grep -oP '(?<=overlay2\.size=)\d+' || echo 0"""
    status_code, output = await run_command(host, cmd)
    if status_code != 0 or not output:
        return 0
    return int(output.strip())


async def get_ib_dev_info(host=''):
    status_code, output = await run_command(host, 'ibstat')
    if status_code != 0:
        return []
    pattern = re.compile(r"CA '(\S+)'[\s\S]*?Node GUID: (\S+)[\s\S]*?Rate: (\d+)")
    matches = pattern.findall(output)
    ib_infos = []
    for match in matches:
        ca, guid, rate = match
        ib_infos.append({"ca": ca, "guid": guid, "rate": int(rate)})
    return ib_infos


async def get_nic_info(host=''):
    status_code, output = await run_command(host, 'ip  -j a')
    if status_code != 0:
        return []
    ret_j = json.loads(output)
    return ret_j


async def get_nvidia_gpu_info(host):
    data = []
    cmd = 'nvidia-smi --query-gpu=uuid,memory.total,name --format=csv,noheader  | paste -sd ":" '
    status_code, output = await run_command(host, cmd)
    if status_code == 0:
        items = output.split(":")
        for item in items:
            l = item.split(",")
            data.append({
                "uuid": l[0],
                "gpu_model": l[2].strip("+"),
                "memory": math.ceil(float(l[1].strip().split(" ")[0]) / 1024)
            })
    return data


async def get_hfk_gpu_info(host=''):
    data = []
    cmd = "hxsmi query  | grep -E 'SN|Total|Product Name' | grep -v Space"
    status_code, output = await run_command(host, cmd, timeout=120)
    if status_code == 0:
        pattern = r"Product Name\s*:\s*(.*?)\s*SN\s*:\s*(.*?)\s*Total\s*:\s*(.*?)\s*MiB"
        matches = re.findall(pattern, output)
        for item in matches:
            data.append({
                "uuid": item[1],
                "gpu_model": item[0],
                "memory": math.ceil(int(item[2]) / 1024)
            })
    return data


async def get_huawei_gpu_info(host):
    cmd1 = "npu-smi info -t board -i index -c 0 | grep VDie | awk -F':' '{print $2}' "
    cmd2 = "npu-smi info -t memory -i 0 | grep 'HBM Capacity(MB)' | awk -F' ' '{print $4}'"
    cmd3 = 'npu-smi info -l | grep "NPU ID"'
    status_code, output = await run_command(host, cmd3, timeout=20)
    data = []
    ids = []
    if status_code == 0:
        ids = re.findall(r'NPU ID\s*:\s*(\d+)', output)
        ids = list(map(int, ids))
    for index in ids:
        get_npu_model_cmd = "npu-smi info  -m | awk 'NR==2 {print $(NF-1), $NF}'"
        _, gpu_model = await run_command(host, get_npu_model_cmd, timeout=20)
        if "310" in gpu_model:
            cmd1 = "npu-smi info -t board -i index -c 0 | grep 'Die ID' | awk -F':' '{print $2}' | xargs"
            cmd2 = "npu-smi info -t memory -i index | grep 'Total DDR Capacity(MB)' | awk -F':' '{print $2}' | xargs"
        status_code, output = await run_command(host, cmd1.replace("index", str(index)), timeout=20)
        if status_code == 0:
            uuid = output.replace(" ", "-").strip("-").strip()
            status_code2, output2 = await run_command(host, cmd2.replace("index", str(index)), timeout=20)
            memory = math.ceil(int(output2.strip().split(" ")[0]) / 1024)
            data.append({
                "uuid": uuid,
                "gpu_model": gpu_model.strip(),
                "memory": memory
            })
    return data


async def get_hygon_dcu_info(host):
    data = []
    cmd = "source /usr/local/hygon/env.sh;hy-smi --showserial --showmemavailable  --json"
    status_code, output = await run_command(host, cmd, timeout=120)
    json_output = json.loads(output)
    logger.info("get hygon dcu info [%s]", json_output)
    get_dcu_model = " lspci -vvv  -mm  | grep -i 'haiguang' -C 2 | grep SDevice | awk 'NR==1 {print $NF}' "
    _, gpu_model = await run_command(host, get_dcu_model, timeout=20)
    for key, item in json_output.items():
        data.append({
            "uuid": item.get("Serial Number"),
            "gpu_model": gpu_model,
            "memory": math.ceil(int(item.get("Available memory size (MiB)")) / 1024)
        })
    return data


gpu_handle_map = {
    "nvidia": get_nvidia_gpu_info,
    "hfk": get_hfk_gpu_info,
    "huawei": get_huawei_gpu_info,
    "hygon": get_hygon_dcu_info
}


@asynccontextmanager
async def get_async_session() -> AsyncSession:
    async_session = sessionmaker(bind=async_engine, class_=AsyncSession, expire_on_commit=False)
    async with async_session() as session:
        yield session
        await session.commit()
    logger.debug(f"session commit")


def remove_dot_key(d: dict) -> dict:
    """
    recursion remove key has dot
    :param d:
    :return:
    """
    if isinstance(d, list):
        for i in d:
            remove_dot_key(i)
    if isinstance(d, dict):
        for dot_key in [k for k in d.keys() if "." in k or k.startswith("f:")]:
            del d[dot_key]

        for k, v in d.items():
            remove_dot_key(v)
    return d


async def load_kube_config():
    """
    async load kube config
    """
    try:
        config.load_incluster_config()
        logger.info(f"load in cluster config")
    except ConfigException:
        await config.load_kube_config()
        logger.info(f"load kube config")


async def update_model(instance: Union[Notebook, Train]):
    """
    update model
    :param instance:
    """
    async with get_async_session() as session:
        session.add(instance)


def is_unschedulable(reason: str):
    """
    判断是否为Unschedulable状态
    :param reason:
    :return:
    """
    if "Unschedulable" in reason and "PersistentVolumeClaims" not in reason:
        return True
    return False


def get_train_running_time(train_cr: Dict):
    """
    get train running time
    :param train_cr:
    :return:
    """
    for condition in train_cr["status"].get("conditions"):
        if condition.get("type") == TrainStatus.Running:
            return utc_str_to_local_datetime(condition.get("lastTransitionTime"))
    return None


def get_train_latest_status(train_cr: Dict):
    """
    get train latest status
    :param train_cr:
    :return:
    """
    latest_status_conditions = train_cr["status"].get("conditions")[-1]
    type_ = latest_status_conditions.get("type")
    if type_ == "Running" and train_cr["spec"].get("runPolicy", {}).get("suspend", False):
        return STATUS_PHASE.Suspended
    return type_


def get_train_ended_time(train_cr: Dict):
    """
    get train ended time
    :param train_cr:
    :return:
    """
    for condition in train_cr["status"].get("conditions"):
        if condition.get("type") in END_STATUS:
            return utc_str_to_local_datetime(condition.get("lastTransitionTime"))
    return None


UTC_FORMAT = "%Y-%m-%dT%H:%M:%SZ"


def utc_str_to_local_datetime(utc_str: str) -> datetime.datetime:
    """parse utc time string to local time string

    :param utc_str: UTC time string
    :param utc_format: format of UTC time string
    :param local_format: format of local time string
    :return: local time string
    """
    return datetime.datetime.strptime(utc_str, UTC_FORMAT) \
        .replace(tzinfo=datetime.timezone.utc).astimezone(DEFAULT_TZINFO).replace(tzinfo=None)


END_STATUS = [STATUS_PHASE.Succeeded, STATUS_PHASE.Failed, STATUS_PHASE.Terminating, STATUS_PHASE.Terminated,
              STATUS_PHASE.Completed, STATUS_PHASE.Deleted]  # , STATUS_PHASE.CreateFailed]

NOTEBOOK_END_STATUS = [STATUS_PHASE.Succeeded, STATUS_PHASE.Failed, STATUS_PHASE.Completed, STATUS_PHASE.Deleted,
                       STATUS_PHASE.CreateFailed, STATUS_PHASE.Terminated]


async def trains_unschedulable(kind, name, namespace):
    try:
        events = await Event.list(namespace=namespace,
                                  field_selector=f"involvedObject.kind={kind},involvedObject.name={name}")
        for e in events:
            if e.raw["type"] == EVENT_TYPE_WARNING and "Unschedulable" == e.raw["reason"]:
                return True
        return False
    except Exception as e:
        logger.error(f"get events error: {e}")
    return False


async def insert_notebook_action_record(notebook: Notebook):
    """
    # 创建操作记录
    :param notebook:
    """
    record = OperationRecord(
        user_id='system', resource=notebook.uuid, child_resource=notebook.uuid,
        status=notebook.status, reason=notebook.reason, action=notebook.status
    )
    async with get_async_session() as session:
        session.add(record)


async def update_train_status(action: str, train_cr: Dict):
    """
    :return:
    """
    async with lock_manager.get_lock(train_cr):
        if not instance_versions.is_latest_version(train_cr):
            logger.info(
                f"skip old version "
                f"{train_cr['kind']}:{train_cr['metadata']['name']}:{train_cr['metadata']['resourceVersion']}, "
                f"max version is : {instance_versions.get_max_version(train_cr)}")
            return  # skip old version

        kind, name = train_cr["kind"], train_cr["metadata"]["name"]
        if not train_cr["status"].get("conditions", []):
            logger.info(f"{kind}:{name} status.conditions is empty")
            return
        logger.info(
            f"process {action}:{kind}:{name}:{train_cr['metadata']['resourceVersion']}")

        billing_enable: bool = False
        train: Train
        async with get_async_session() as session:
            stmt = select(Train).where(Train.uuid == train_cr['metadata']['name']) \
                .options(selectinload(Train.replica_specs))
            sr: ScalarResult[Train] = await session.scalars(stmt)
            train = sr.first()
            if not train:
                return
            billing_enable = train.billing_enable
            if not train.kuid:
                train.kuid = train_cr["metadata"]["uid"]

        logger.info(f"get train [{train.uuid}] previous status {train.status}")
        if train.status in END_STATUS:
            # 已完成的状态不再更新
            logger.info(f"{train.endpoint}/{train.uuid} status is {train.status}, no need to update")
            instance_versions.delete(train_cr)
            if app.settings.IAAS_MESSAGE_ENABLE:
                # 根据状态发送短信通知
                if train.status in [STATUS_PHASE.Completed, STATUS_PHASE.Succeeded]:
                    stmt_op = select(OperationRecord).where(
                        (OperationRecord.resource == train.uuid) & (
                                OperationRecord.action == TrainMessageStatusAction.Train_Done))
                    sr_op: ScalarResult[OperationRecord] = await session.scalars(stmt_op)
                    operation_record = sr_op.first()
                    if operation_record is not None:
                        return
                    # Send message to IAAS.
                    user_info = get_qingcloud_user_info_by_user_id(train.user_id)
                    send_data = {
                        "username": user_info.get("user_name"),
                        "zone": ZONE_INFO,
                        "name": train.name,
                        "id": train.uuid,
                        "action": "训练任务已完成"
                    }
                    send_message_request(AICP_JOB_STATUS, train.user_id, json.dumps(send_data))
                    # Add Record info to pg.
                    record = OperationRecord(
                        user_id=train.user_id, resource=train.uuid, child_resource=train.uuid,
                        status="SUCCEED", action=TrainMessageStatusAction.Train_Done
                    )
                    session.add(record)
                    await session.commit()
                    logger.info(f"User {train.user_id} has sent message when train status changing.")
                    return
                if train.status == STATUS_PHASE.Failed:
                    stmt_op = select(OperationRecord).where(
                        (OperationRecord.resource == train.uuid) & (
                                OperationRecord.action == TrainMessageStatusAction.Train_Failed))
                    sr_op: ScalarResult[OperationRecord] = await session.scalars(stmt_op)
                    operation_record = sr_op.first()
                    if operation_record is not None:
                        return
                    # Send message to IAAS.
                    user_info = get_qingcloud_user_info_by_user_id(train.user_id)
                    send_data = {
                        "username": user_info.get("user_name"),
                        "zone": ZONE_INFO,
                        "name": train.name,
                        "id": train.uuid,
                        "action": "训练任务失败"
                    }
                    send_message_request(AICP_JOB_STATUS, train.user_id, json.dumps(send_data))
                    # Add Record info to pg.
                    record = OperationRecord(
                        user_id=train.user_id, resource=train.uuid, child_resource=train.uuid,
                        status="SUCCEED", action=TrainMessageStatusAction.Train_Failed
                    )
                    session.add(record)
                    await session.commit()
                    logger.info(f"User {train.user_id} has sent message when train status changing.")
                    return
                if train.status in [STATUS_PHASE.Terminating, STATUS_PHASE.Terminated]:
                    stmt_op = select(OperationRecord).where(
                        (OperationRecord.resource == train.uuid) & (
                                OperationRecord.action == TrainMessageStatusAction.Train_TERMINATED))
                    sr_op: ScalarResult[OperationRecord] = await session.scalars(stmt_op)
                    operation_record = sr_op.first()
                    if operation_record is not None:
                        return
                    # Send message to IAAS.
                    user_info = get_qingcloud_user_info_by_user_id(train.user_id)
                    send_data = {
                        "username": user_info.get("user_name"),
                        "zone": ZONE_INFO,
                        "name": train.name,
                        "id": train.uuid,
                        "action": "训练任务终止"
                    }
                    send_message_request(AICP_JOB_STATUS, train.user_id, json.dumps(send_data))
                    # Add Record info to pg.
                    record = OperationRecord(
                        user_id=train.user_id, resource=train.uuid, child_resource=train.uuid,
                        status="SUCCEED", action=TrainMessageStatusAction.Train_TERMINATED
                    )
                    session.add(record)
                    await session.commit()
                    logger.info(f"User {train.user_id} has sent message when train status changing.")
                return

        # 不可调度
        if app.settings.DELETE_IMMEDIATELY_WHEN_UNSCHEDULABLE:
            is_trains_unschedulable = await trains_unschedulable(train_cr["kind"], train.uuid, train.namespace)
            if is_trains_unschedulable:
                train.status = STATUS_PHASE.CreateFailed
                train.reason = "Unschedulable"
                if billing_enable:
                    async_pool.submit_sync(QAIBillingService().unlease, train.uuid, train.user_id)
                await update_model(train)
                try:
                    train_kr8s_cr = await kr8s.asyncio.get(train.endpoint, train.uuid, namespace=train.namespace)
                    if train_kr8s_cr:
                        await train_kr8s_cr[0].delete()
                except Exception as e:
                    logger.error(f"delete notebook error: {e}")
                return

        # 更新运行时间
        running_at = get_train_running_time(train_cr)
        if not train.running_at and running_at:
            train.running_at = running_at

        status = get_train_latest_status(train_cr)
        logger.info(f"tran [{train.uuid}] status is {status}")
        if action == "DELETED":
            status = TrainStatus.Terminated
            train.ended_at = datetime.datetime.now()
            train.reason = train.reason or "delete by system"

        if action == "MODIFIED":
            cr_status = train_cr["status"].get("conditions")[-1].get("type", "")
            if cr_status == "Restarting":
                logger.info("分布式任务重试")
                # IAAS send message.
                user_info = get_qingcloud_user_info_by_user_id(train.user_id)
                send_data = {
                    "username": user_info.get("user_name"),
                    "zone": ZONE_INFO,
                    "name": train.name,
                    "id": train.uuid,
                }
                async_pool.submit_sync(send_message_request, AICP_JOB_RESTART, train.user_id, json.dumps(send_data))

        ended_at = get_train_ended_time(train_cr)
        if not train.ended_at and ended_at:
            train.ended_at = ended_at

        if status in END_STATUS:
            async_pool.submit_sync(QAIBillingService().unlease, train.uuid, train.user_id)
            if train_cr['metadata']['name'].startswith("ft"):
                async with client.ApiClient() as api:
                    core_api = client.CoreV1Api(api)
                    logger.info(f"delete ft cm {train_cr['metadata']['name']}")
                    await core_api.delete_namespaced_config_map(train_cr["metadata"]["name"], train_cr["metadata"]["namespace"])
                    await core_api.delete_namespaced_config_map(f"{train_cr['metadata']['name']}-datainfo",
                                           train_cr["metadata"]["namespace"])
        # 从未运行变为运行
        if train.status != status and status == TrainStatus.Running:
            if train.replica_specs[0].resource.billing_enable:
                async_pool.submit_sync(QAIBillingService().restart,
                                       train.uuid, train.user_id, None, train.replica_specs[0].replicas)

        train.status = status
        # 适配暂停后重启
        if status == TrainStatus.Suspended and train.reason == "重启中":
            if app.settings.VOLCANO_ENABLE:
                train.status = TrainStatus.Inqueuing
            else:
                train.status = TrainStatus.Restarting

        # volcano
        if app.settings.VOLCANO_ENABLE and train.status == STATUS_PHASE.Created:
            train.status = STATUS_PHASE.Inqueuing
        await update_model(train)


async def delete_ufm_info(user_id: str, hostnames: List[str]):
    logger.info(f"delete_ufm_info: {hostnames}")
    guids: List[str]
    pkey: UFMPkeys
    async with get_async_session() as session:
        # 获取需要删除的guids
        stmt = select(IbDevInfo.guid).where(IbDevInfo.hostname.in_(hostnames))
        sr: ScalarResult[IbDevInfo] = await session.exec(stmt)
        guids = sr.all()
        # 获取pkey
        stmt = select(UFMPkeys.pkey).where(UFMPkeys.user_id == user_id)
        sr: ScalarResult[UFMPkeys] = await session.scalars(stmt)
        pkey = sr.first()

    # 发送UFM
    await async_unbind_ufm_pkey_guid(guids, pkey)
    # 获取已存在的guids
    now_guids = []
    async with get_async_session() as session:
        stmt = select(IbDevInfo.guid).where(IbDevInfo.pkey == pkey)
        sr: ScalarResult[IbDevInfo] = await session.exec(stmt)
        exist_guids = sr.all()

        stmt = update(IbDevInfo).where(IbDevInfo.guid.in_(guids)).values(pkey='')
        await session.execute(stmt)

        if len(exist_guids) <= len(guids):
            # 判断是否为空
            stmt = select(IbDevInfo.guid).where(IbDevInfo.pkey == pkey)
            sr: ScalarResult[IbDevInfo] = await session.exec(stmt)
            now_guids = sr.all()

    if len(now_guids) == 0:
        await async_add_pkey_request(pkey)

    logger.info(f"delete_ufm_info_done: {hostnames}")


async def create_ufm_info(user_id: str, hostnames: List[str]):
    logger.info("Create ufm start.")
    async with get_async_session() as session:
        # 获取需要新增的guids
        stmt = select(IbDevInfo.guid).where(IbDevInfo.hostname.in_(hostnames))
        sr: ScalarResult[IbDevInfo] = await session.exec(stmt)
        guids = sr.all()
        # 获取pkey
        stmt = select(UFMPkeys.pkey).where(UFMPkeys.user_id == user_id)
        sr: ScalarResult[UFMPkeys] = await session.scalars(stmt)
        pkey = sr.first()
        # 获取已存在的guids
        stmt = select(IbDevInfo.guid).where(IbDevInfo.pkey == pkey)
        sr: ScalarResult[IbDevInfo] = await session.exec(stmt)
        exist_guids = sr.all()
        guids.extend(exist_guids)
    logger.info("Create ufm info done.")

    # 发送UFM
    await async_bind_ufm_pkey_guids(guids, pkey)

    # 修改db

    async with get_async_session() as session:
        # update
        stmt = update(IbDevInfo).where(IbDevInfo.guid.in_(guids)).values(pkey=pkey)
        await session.execute(stmt)


async def update_invalid_notebook_status():
    """
    update invalid notebook status

    更新所有无效的notebook状态(数据库里面显示仍然运行的, 但是实际已经被删除的. )
    """
    notebooks_cr: List[Dict]
    async with client.ApiClient() as api:
        v1 = client.CustomObjectsApi(api)
        notebooks_cr = await v1.list_cluster_custom_object(
            group=NoteBookObj.version.split("/")[0], version=NoteBookObj.version.split("/")[1],
            plural=NoteBookObj.plural
        )
    valid_notebooks_name = set([notebook_cr["metadata"]["name"] for notebook_cr in notebooks_cr["items"]])

    billing_enable_map: Dict[str, bool]
    async with get_async_session() as session:
        stmt = select(Notebook).where(Notebook.status.in_(SearchableNotebookStatus)) \
            .options(selectinload(Notebook.replica_specs))
        sr: ScalarResult[Notebook] = await session.scalars(stmt)
        notebooks = sr.all()
        if not notebooks:
            return
        billing_enable_map = {n.uuid: n.billing_enable for n in notebooks}

    for notebook in notebooks:
        if notebook.uuid not in valid_notebooks_name:
            logger.warning(f"Notebook/{notebook.uuid} not found,status is {notebook} and update status to Terminated")
            notebook.status = STATUS_PHASE.Terminated
            await update_model(notebook)
            if billing_enable_map.get(notebook.uuid, False):
                async_pool.submit_sync(QAIBillingService().unlease, notebook.uuid, notebook.user_id)


async def update_invalid_train_status(endpoint: str):
    """

    :return:
    """
    if not app.settings.DELETE_IMMEDIATELY_WHEN_UNSCHEDULABLE:
        return

    billing_enable_map: Dict[str, bool]
    async with get_async_session() as session:
        stmt = select(Train).where(Train.status == TrainStatus.CreateFailed, Train.endpoint == endpoint) \
            .options(selectinload(Train.replica_specs))
        sr: ScalarResult[Train] = await session.scalars(stmt)
        invalid_trains = sr.all()
        if not invalid_trains:
            return
        billing_enable_map = {t.uuid: t.billing_enable for t in invalid_trains}

    invalid_trains_uuid = {t.uuid: t for t in invalid_trains}
    trains_cr = await kr8s.asyncio.get(endpoint, namespace=kr8s.ALL)

    invalid_trains_cr = [train_cr for train_cr in trains_cr if train_cr["metadata"]["name"] in invalid_trains_uuid]
    for invalid_train_cr in invalid_trains_cr:
        logger.warning(
            f"{invalid_train_cr['kind']}/{invalid_train_cr['metadata']['name']} not found, update status to Terminated")
        await invalid_trains_cr.delete()
        if billing_enable_map.get(invalid_train_cr["metadata"]["name"], False):
            invalid_train = invalid_trains_uuid[invalid_train_cr["metadata"]["name"]]
            async_pool.submit_sync(QAIBillingService().unlease, invalid_train.uud, invalid_train.user_id)


async def auto_stop_notebook():
    """
    auto stop notebook
    """
    while True:
        try:
            async with get_async_session() as session:
                stmt = select(Notebook).where(
                    Notebook.status == STATUS_PHASE.Running, Notebook.stop_time.is_not(None),
                    Notebook.stop_time <= datetime.datetime.now()
                ).options(selectinload(Notebook.replica_specs))
                sr: ScalarResult[Notebook] = await session.scalars(stmt)
                notebooks = sr.all()
                for n in notebooks:
                    async_pool.submit_sync(stop_notebooks_in_background, n.uuid)

        except Exception as e:
            logger.exception(f"auto_stop_notebook error: {e}")

        await asyncio.sleep(60)


async def send_stop_message():
    """
    When container stop send message.
    :return:
    """
    while True:
        try:
            # before 24 hours.
            async with get_async_session() as session:
                # 如果距离结束时间小于23小时58分，则不发
                stmt = select(Notebook).where(
                    Notebook.status == STATUS_PHASE.Running, Notebook.stop_time.is_not(None),
                    Notebook.stop_time <= datetime.datetime.now() + datetime.timedelta(days=1),
                    datetime.datetime.now() + datetime.timedelta(hours=23, minutes=58) <= Notebook.stop_time
                ).options(selectinload(Notebook.replica_specs))
                sr: ScalarResult[Notebook] = await session.scalars(stmt)
                notebooks = sr.all()
                for n in notebooks:
                    # select not send container.
                    stmt_op = select(OperationRecord).where(
                        (OperationRecord.resource == n.uuid) & (
                                OperationRecord.action == NotebookMessageStopAction.Days_1_Stop))
                    sr_op: ScalarResult[OperationRecord] = await session.scalars(stmt_op)
                    operation_record = sr_op.first()
                    if operation_record is not None:
                        continue
                    # IAAS send message.
                    user_info = get_qingcloud_user_info_by_user_id(n.user_id)
                    send_data = {
                        "username": user_info.get("user_name"),
                        "zone": ZONE_INFO,
                        "name": n.name,
                        "id": n.uuid,
                        "spec": f"{n.replica_specs.custom_cpu}核 {n.replica_specs.custom_memory}G {n.replica_specs.custom_gpu_type} {n.replica_specs.custom_gpu_memory}G * {n.replica_specs.custom_gpu}" if n.replica_specs.custom_gpu > 0 else f"{n.replica_specs.custom_cpu}核 {n.replica_specs.custom_memory}G",
                        "expire_time": n.stop_time.strftime("%Y-%m-%d %H:%M:%S")
                    }
                    async_pool.submit_sync(send_message_request, INSTANCE_SHUTDOWN, n.user_id, json.dumps(send_data))
                    # Add Record info to pg.
                    record = OperationRecord(
                        user_id=n.user_id, resource=n.uuid, child_resource=n.uuid,
                        status="SUCCEED", action=NotebookMessageStopAction.Days_1_Stop
                    )
                    session.add(record)
                    await session.commit()
                    logger.info(f"User {n.user_id} has sent message before container stop 24 hours.")

            # before 2 hours.
            async with get_async_session() as session:
                # 如果距离结束时间小于1小时58分，则不发
                stmt = select(Notebook).where(
                    Notebook.status == STATUS_PHASE.Running, Notebook.stop_time.is_not(None),
                    Notebook.stop_time <= datetime.datetime.now() + datetime.timedelta(hours=2),
                    datetime.datetime.now() + datetime.timedelta(hours=1, minutes=58) <= Notebook.stop_time
                ).options(selectinload(Notebook.replica_specs))
                sr: ScalarResult[Notebook] = await session.scalars(stmt)
                notebooks = sr.all()
                for n in notebooks:
                    # select not send container.
                    stmt_op = select(OperationRecord).where(
                        (OperationRecord.resource == n.uuid) & (
                                OperationRecord.action == NotebookMessageStopAction.Hours_2_Stop))
                    sr_op: ScalarResult[OperationRecord] = await session.scalars(stmt_op)
                    operation_record = sr_op.first()
                    if operation_record is not None:
                        continue
                    # IAAS send message.
                    user_info = get_qingcloud_user_info_by_user_id(n.user_id)
                    send_data = {
                        "username": user_info.get("user_name"),
                        "zone": ZONE_INFO,
                        "name": n.name,
                        "id": n.uuid,
                        "spec": f"{n.replica_specs.custom_cpu}核 {n.replica_specs.custom_memory}G {n.replica_specs.custom_gpu_type} {n.replica_specs.custom_gpu_memory}G * {n.replica_specs.custom_gpu}" if n.replica_specs.custom_gpu > 0 else f"{n.replica_specs.custom_cpu}核 {n.replica_specs.custom_memory}G",
                        "expire_time": n.stop_time.strftime("%Y-%m-%d %H:%M:%S")
                    }
                    async_pool.submit_sync(send_message_request, INSTANCE_SHUTDOWN, n.user_id, json.dumps(send_data))
                    # Add Record info to pg.
                    record = OperationRecord(
                        user_id=n.user_id, resource=n.uuid, child_resource=n.uuid,
                        status="SUCCEED", action=NotebookMessageStopAction.Hours_2_Stop
                    )
                    session.add(record)
                    await session.commit()
                    logger.info(f"User {n.user_id} has sent message before container stop 2 hours.")

            # before 10 minutes.
            async with get_async_session() as session:
                # 如果距离结束时间小于8分，则不发
                stmt = select(Notebook).where(
                    Notebook.status == STATUS_PHASE.Running, Notebook.stop_time.is_not(None),
                    Notebook.stop_time <= datetime.datetime.now() + datetime.timedelta(minutes=10),
                    datetime.datetime.now() + datetime.timedelta(minutes=8) <= Notebook.stop_time
                ).options(selectinload(Notebook.replica_specs))
                sr: ScalarResult[Notebook] = await session.scalars(stmt)
                notebooks = sr.all()
                for n in notebooks:
                    # select not send container.
                    stmt_op = select(OperationRecord).where(
                        (OperationRecord.resource == n.uuid) & (
                                OperationRecord.action == NotebookMessageStopAction.Minutes_10_Stop))
                    sr_op: ScalarResult[OperationRecord] = await session.scalars(stmt_op)
                    operation_record = sr_op.first()
                    if operation_record is not None:
                        continue
                    # IAAS send message.
                    user_info = get_qingcloud_user_info_by_user_id(n.user_id)
                    send_data = {
                        "username": user_info.get("user_name"),
                        "zone": ZONE_INFO,
                        "name": n.name,
                        "id": n.uuid,
                        "spec": f"{n.replica_specs.custom_cpu}核 {n.replica_specs.custom_memory}G {n.replica_specs.custom_gpu_type} {n.replica_specs.custom_gpu_memory}G * {n.replica_specs.custom_gpu}" if n.replica_specs.custom_gpu > 0 else f"{n.replica_specs.custom_cpu}核 {n.replica_specs.custom_memory}G",
                        "expire_time": n.stop_time.strftime("%Y-%m-%d %H:%M:%S")
                    }
                    async_pool.submit_sync(send_message_request, INSTANCE_SHUTDOWN, n.user_id, json.dumps(send_data))
                    # Add Record info to pg.
                    record = OperationRecord(
                        user_id=n.user_id, resource=n.uuid, child_resource=n.uuid,
                        status="SUCCEED", action=NotebookMessageStopAction.Minutes_10_Stop
                    )
                    session.add(record)
                    await session.commit()
                    logger.info(f"User {n.user_id} has sent message before container stop 10 minutes.")

            # now.
            async with get_async_session() as session:
                stmt = select(Notebook).where(
                    Notebook.status == STATUS_PHASE.Running,
                    Notebook.stop_time.is_not(None),
                    Notebook.stop_time <= datetime.datetime.now()
                ).options(selectinload(Notebook.replica_specs))
                sr: ScalarResult[Notebook] = await session.scalars(stmt)
                notebooks = sr.all()
                for n in notebooks:
                    # select not send container.
                    stmt_op = select(OperationRecord).where(
                        (OperationRecord.resource == n.uuid) & (
                                OperationRecord.action == NotebookMessageStopAction.NowStop))
                    sr_op: ScalarResult[OperationRecord] = await session.scalars(stmt_op)
                    operation_record = sr_op.first()
                    if operation_record is not None:
                        continue
                    # IAAS send message.
                    user_info = get_qingcloud_user_info_by_user_id(n.user_id)
                    send_data = {
                        "username": user_info.get("user_name"),
                        "zone": ZONE_INFO,
                        "name": n.name,
                        "id": n.uuid,
                        "spec": f"{n.replica_specs.custom_cpu}核 {n.replica_specs.custom_memory}G {n.replica_specs.custom_gpu_type} {n.replica_specs.custom_gpu_memory}G * {n.replica_specs.custom_gpu}" if n.replica_specs.custom_gpu > 0 else f"{n.replica_specs.custom_cpu}核 {n.replica_specs.custom_memory}G",
                        "expire_time": n.stop_time.strftime("%Y-%m-%d %H:%M:%S")
                    }
                    async_pool.submit_sync(send_message_request, INSTANCE_SHUTDOWN, n.user_id, json.dumps(send_data))
                    # Add Record info to pg.
                    record = OperationRecord(
                        user_id=n.user_id, resource=n.uuid, child_resource=n.uuid,
                        status="SUCCEED", action=NotebookMessageStopAction.NowStop
                    )
                    session.add(record)
                    await session.commit()
                    logger.info(f"User {n.user_id} has sent message when container stopped.")

        except Exception as e:
            logger.exception(f"send message error: {e}")

        await asyncio.sleep(60)


async def send_delete_message():
    """
    When container delete send message.
    :return:
    """
    while True:
        try:
            # before 24 hours.
            async with get_async_session() as session:
                stmt = select(Notebook).where(
                    Notebook.auto_delete_time.is_not(None),
                    Notebook.auto_delete_time <= datetime.datetime.now() + datetime.timedelta(days=1),
                    datetime.datetime.now() + datetime.timedelta(hours=23, minutes=58) <= Notebook.auto_delete_time
                ).options(selectinload(Notebook.replica_specs))
                sr: ScalarResult[Notebook] = await session.scalars(stmt)
                notebooks = sr.all()
                for n in notebooks:
                    # select not send container.
                    stmt_op = select(OperationRecord).where(
                        (OperationRecord.resource == n.uuid) & (
                                OperationRecord.action == NotebookMessageReleaseAction.Days_1_Release))
                    sr_op: ScalarResult[OperationRecord] = await session.scalars(stmt_op)
                    operation_record = sr_op.first()
                    if operation_record is not None:
                        continue
                    # IAAS send message.
                    user_info = get_qingcloud_user_info_by_user_id(n.user_id)
                    send_data = {
                        "username": user_info.get("user_name"),
                        "zone": ZONE_INFO,
                        "name": n.name,
                        "id": n.uuid,
                        "spec": f"{n.replica_specs.custom_cpu}核 {n.replica_specs.custom_memory}G {n.replica_specs.custom_gpu_type} {n.replica_specs.custom_gpu_memory}G * {n.replica_specs.custom_gpu}" if n.replica_specs.custom_gpu > 0 else f"{n.replica_specs.custom_cpu}核 {n.replica_specs.custom_memory}G",
                        "expire_time": n.auto_delete_time.strftime("%Y-%m-%d %H:%M:%S")
                    }
                    async_pool.submit_sync(send_message_request, INSTANCE_RELEASE, n.user_id, json.dumps(send_data))
                    # Add Record info to pg.
                    record = OperationRecord(
                        user_id=n.user_id, resource=n.uuid, child_resource=n.uuid,
                        status="SUCCEED", action=NotebookMessageReleaseAction.Days_1_Release
                    )
                    session.add(record)
                    await session.commit()
                    logger.info(f"User {n.user_id} has sent message before container release 24 hours.")

            # before 2 hours.
            async with get_async_session() as session:
                stmt = select(Notebook).where(
                    Notebook.auto_delete_time.is_not(None),
                    Notebook.auto_delete_time <= datetime.datetime.now() + datetime.timedelta(hours=2),
                    datetime.datetime.now() + datetime.timedelta(hours=1, minutes=58) <= Notebook.auto_delete_time
                ).options(selectinload(Notebook.replica_specs))
                sr: ScalarResult[Notebook] = await session.scalars(stmt)
                notebooks = sr.all()
                for n in notebooks:
                    # select not send container.
                    stmt_op = select(OperationRecord).where(
                        (OperationRecord.resource == n.uuid) & (
                                OperationRecord.action == NotebookMessageReleaseAction.Hours_2_Release))
                    sr_op: ScalarResult[OperationRecord] = await session.scalars(stmt_op)
                    operation_record = sr_op.first()
                    if operation_record is not None:
                        continue
                    # IAAS send message.
                    user_info = get_qingcloud_user_info_by_user_id(n.user_id)
                    send_data = {
                        "username": user_info.get("user_name"),
                        "zone": ZONE_INFO,
                        "name": n.name,
                        "id": n.uuid,
                        "spec": f"{n.replica_specs.custom_cpu}核 {n.replica_specs.custom_memory}G {n.replica_specs.custom_gpu_type} {n.replica_specs.custom_gpu_memory}G * {n.replica_specs.custom_gpu}" if n.replica_specs.custom_gpu > 0 else f"{n.replica_specs.custom_cpu}核 {n.replica_specs.custom_memory}G",
                        "expire_time": n.auto_delete_time.strftime("%Y-%m-%d %H:%M:%S")
                    }
                    async_pool.submit_sync(send_message_request, INSTANCE_RELEASE, n.user_id, json.dumps(send_data))
                    # Add Record info to pg.
                    record = OperationRecord(
                        user_id=n.user_id, resource=n.uuid, child_resource=n.uuid,
                        status="SUCCEED", action=NotebookMessageReleaseAction.Hours_2_Release
                    )
                    session.add(record)
                    await session.commit()
                    logger.info(f"User {n.user_id} has sent message before container release 2 hours.")

            # before 10 minutes.
            async with get_async_session() as session:
                stmt = select(Notebook).where(
                    Notebook.auto_delete_time.is_not(None),
                    Notebook.auto_delete_time <= datetime.datetime.now() + datetime.timedelta(minutes=10),
                    datetime.datetime.now() + datetime.timedelta(minutes=8) <= Notebook.auto_delete_time
                ).options(selectinload(Notebook.replica_specs))
                sr: ScalarResult[Notebook] = await session.scalars(stmt)
                notebooks = sr.all()
                for n in notebooks:
                    # select not send container.
                    stmt_op = select(OperationRecord).where(
                        (OperationRecord.resource == n.uuid) & (
                                OperationRecord.action == NotebookMessageReleaseAction.Minutes_10_Release))
                    sr_op: ScalarResult[OperationRecord] = await session.scalars(stmt_op)
                    operation_record = sr_op.first()
                    if operation_record is not None:
                        continue
                    # IAAS send message.
                    user_info = get_qingcloud_user_info_by_user_id(n.user_id)
                    send_data = {
                        "username": user_info.get("user_name"),
                        "zone": ZONE_INFO,
                        "name": n.name,
                        "id": n.uuid,
                        "spec": f"{n.replica_specs.custom_cpu}核 {n.replica_specs.custom_memory}G {n.replica_specs.custom_gpu_type} {n.replica_specs.custom_gpu_memory}G * {n.replica_specs.custom_gpu}" if n.replica_specs.custom_gpu > 0 else f"{n.replica_specs.custom_cpu}核 {n.replica_specs.custom_memory}G",
                        "expire_time": n.auto_delete_time.strftime("%Y-%m-%d %H:%M:%S")
                    }
                    async_pool.submit_sync(send_message_request, INSTANCE_RELEASE, n.user_id, json.dumps(send_data))
                    # Add Record info to pg.
                    record = OperationRecord(
                        user_id=n.user_id, resource=n.uuid, child_resource=n.uuid,
                        status="SUCCEED", action=NotebookMessageReleaseAction.Minutes_10_Release
                    )
                    session.add(record)
                    await session.commit()
                    logger.info(f"User {n.user_id} has sent message before container release 10 minutes.")

            # now.
            async with get_async_session() as session:
                stmt = select(Notebook).where(
                    Notebook.auto_delete_time.is_not(None), Notebook.auto_delete_time <= datetime.datetime.now()
                ).options(selectinload(Notebook.replica_specs))
                sr: ScalarResult[Notebook] = await session.scalars(stmt)
                notebooks = sr.all()
                for n in notebooks:
                    # select not send container.
                    stmt_op = select(OperationRecord).where(
                        (OperationRecord.resource == n.uuid) & (
                                OperationRecord.action == NotebookMessageReleaseAction.NowRelease))
                    sr_op: ScalarResult[OperationRecord] = await session.scalars(stmt_op)
                    operation_record = sr_op.first()
                    if operation_record is not None:
                        continue
                    # IAAS send message.
                    user_info = get_qingcloud_user_info_by_user_id(n.user_id)
                    send_data = {
                        "username": user_info.get("user_name"),
                        "zone": ZONE_INFO,
                        "name": n.name,
                        "id": n.uuid,
                        "spec": f"{n.replica_specs.custom_cpu}核 {n.replica_specs.custom_memory}G {n.replica_specs.custom_gpu_type} {n.replica_specs.custom_gpu_memory}G * {n.replica_specs.custom_gpu}" if n.replica_specs.custom_gpu > 0 else f"{n.replica_specs.custom_cpu}核 {n.replica_specs.custom_memory}G",
                        "expire_time": n.auto_delete_time.strftime("%Y-%m-%d %H:%M:%S")
                    }
                    async_pool.submit_sync(send_message_request, INSTANCE_RELEASE, n.user_id, json.dumps(send_data))
                    # Add Record info to pg.
                    record = OperationRecord(
                        user_id=n.user_id, resource=n.uuid, child_resource=n.uuid,
                        status="SUCCEED", action=NotebookMessageReleaseAction.NowRelease
                    )
                    session.add(record)
                    await session.commit()
                    logger.info(f"User {n.user_id} has sent message when container released.")

        except Exception as e:
            logger.exception(f"send message error: {e}")

        await asyncio.sleep(60)


async def auto_delete_notebook():
    """
    auto stop notebook
    """
    skip_delete_cache_key_prefix = "notebook:skip_delete"
    cache_ttl = 3600 * 24
    while True:
        try:
            async with get_async_session() as session:
                clauses = [and_(Notebook.auto_delete_time.is_not(None), Notebook.auto_delete_time <= datetime.datetime.now())]
                if app.settings.NOTEBOOK_RETENTION_TIME > 0:
                    clauses.append(
                        and_(
                            Notebook.status == NotebookStatus.Suspended,
                            Notebook.updated_at <= datetime.datetime.now() - datetime.timedelta(days=app.settings.NOTEBOOK_RETENTION_TIME))
                    )

                stmt = select(Notebook).where(or_(*clauses)) \
                    .options(selectinload(Notebook.replica_specs)) \
                    .options(selectinload(Notebook.volume_specs))
                sr: ScalarResult[Notebook] = await session.scalars(stmt)
                notebooks = sr.all()
                for n in notebooks:
                    if RedisClient().get(f"{skip_delete_cache_key_prefix}:{n.uuid}"):
                        continue
                    logger.info(f"auto delete notebook: {n.uuid}")
                    if n.replica_specs.rg_id and n.status == 'Suspended':
                        logger.info(f"skip delete notebook in resource group: {n.replica_specs.rg_id}")
                        RedisClient().set(f"{skip_delete_cache_key_prefix}:{n.uuid}", n.replica_specs.rg_id, ex=cache_ttl)
                        continue
                    lease_info = QAIBillingService().get_lease_info(n.uuid, n.user_id)
                    contract_info = lease_info.get("contract", {})
                    if contract_info.get("charge_mode") == "monthly" and lease_info.get("status") == "active":
                        logger.info(f"skip delete notebook with active monthly contract: {contract_info.get('contract_id')}")
                        RedisClient().set(f"{skip_delete_cache_key_prefix}:{n.uuid}", contract_info.get('contract_id'), ex=cache_ttl)
                        continue
                    n.status = NotebookStatus.Terminating
                    n.reason = "定时删除"
                    n.auto_delete_time = None
                    n.unlease()
                    await session.commit()
                    await session.refresh(n)
                    try:
                        notebook_cr = await NoteBookObj.get(n.uuid, namespace=n.namespace)
                        logger.debug(f"fetched uid {n.uuid} notebooks: {notebooks}, to delete it")
                        await notebook_cr.delete()
                    except NotFoundError as e:
                        logger.warning(f"notebook {n.uuid} not found in k8s, then delete it in db directly")
                        n.status = NotebookStatus.Terminated
                    except Exception as e:
                        logger.exception(f"delete {n.uuid} failed., reason: {e}, reset delete time")
                        # reset删除时间
                        n.reason = "delete failed"
                        n.auto_delete_time = datetime.datetime.now()
                    await session.commit()
                    await session.refresh(n)
        except Exception as e:
            logger.exception(f"auto_delete_notebook error: {e}")
        await asyncio.sleep(60)


async def auto_delete_resource_node():
    """
    auto stop notebook
    """
    while True:
        logger.info("auto_delete_resource_node")
        try:
            async with get_async_session() as session:
                stmt = select(ResourceNode).where(
                    ResourceNode.status == STATUS_SUSPENDED,
                    ResourceNode.updated_at <= datetime.datetime.now() - datetime.timedelta(days=1)
                )
                sr: ScalarResult[ResourceNode] = await session.scalars(stmt)
                nodes = sr.all()
                for n in nodes:
                    async_pool.submit_sync(ResourceGroupCrud(None).terminated_by_job, n.rg_node_id)

        except Exception as e:
            logger.exception(f"auto_delete_resource_node error: {e}")

        await asyncio.sleep(3600)

transfer_image_tasks =  TransferImageTaskManager()

async def update_notebook_status(action: str, notebook_obj: Dict) -> None:
    """
    update notebook status
    :param notebook_obj:
    :param action:
    :return:
    """
    async with lock_manager.get_lock(notebook_obj):
        if not instance_versions.is_latest_version(notebook_obj):
            logger.info(
                f"skip old version "
                f"{notebook_obj['kind']}:{notebook_obj['metadata']['name']}:{notebook_obj['metadata']['resourceVersion']}, "
                f"max version is : {instance_versions.get_max_version(notebook_obj)}")
            return  # skip old version

        kind, kuid, name = notebook_obj["kind"], notebook_obj["metadata"]["uid"], notebook_obj["metadata"]["name"]
        logger.info(f"process {action}:{kind}:{name}:{notebook_obj['metadata']['resourceVersion']}")
        notebook: Notebook
        running_operation_record: OperationRecord
        async with get_async_session() as session:
            stmt = select(Notebook) \
                .where(Notebook.uuid == name) \
                .options(selectinload(Notebook.replica_specs)) \
                .options(selectinload(Notebook.volume_specs))
            sr: ScalarResult[Notebook] = await session.scalars(stmt)
            notebook = sr.first()
            if not notebook:
                logger.warning(f"Notebook/{kuid} not found, skip")
                return
            # 获取操作记录
            stmt_op = select(OperationRecord).where(
                (OperationRecord.resource == name) & (OperationRecord.status == NotebookStatus.Running))
            sr_op: ScalarResult[OperationRecord] = await session.scalars(stmt_op)
            running_operation_record = sr_op.first()
        if not notebook.kuid:
            notebook.kuid = kuid

        if action == "DELETED":
            logger.info(f"Delete Notebook/{notebook.uuid}")
            # 压缩zfs
            try:
                zfs_volume = notebook.has_zfs_volume()
                if zfs_volume:
                    logger.info(f"zfs compression: {zfs_volume}")
                    zfs_pvc = LocalStorageVolume(None, notebook.namespace, zfs_volume)
                    async_pool.submit_sync(zfs_pvc.compression)
            except Exception as e:
                logger.exception(f"zfs compression error: {e}")
            await notebook.async_unlease()
            # 获取name
            # 判断是否需要删除UFM分区
            if app.settings.UFM_ENABLE and notebook.replica_specs.custom_infiniband > 0:
                pod = await async_es_client.get_pods_by_labels_app([name])
                hostname = pod[0]["spec"]["nodeName"]
                await delete_ufm_info(notebook.user_id, [hostname])
            await OperationRecord.create_by_resource(notebook).save_async()
            notebook.status = STATUS_PHASE.Terminated
            notebook.charge_status = "ceased"
            await update_model(notebook)
            return

        if notebook.status in DeletedStatus:
            # 已结束不再更新
            logger.info(f"Notebook/{notebook.uuid} status is {notebook.status}, but Terminated status in database, delete it")
            instance_versions.delete(notebook_obj)
            if notebook.status == STATUS_PHASE.Terminated:
                try:
                    # why terminated notebook has modified?
                    nb = await NoteBookObj.get(notebook.uuid, namespace=notebook.namespace)
                    await nb.delete()
                except Exception as e:
                    logger.info(f"Notebook/{notebook.uuid} delete error: {e}")
            return

        k8s_notebook_status: StatusPhase = await async_get_notebook_status(notebook_obj, notebook)

        # check order
        if not notebook.charge_status and k8s_notebook_status.status == STATUS_PHASE.Running:
            rep = await notebook.async_restart_billing(recover=True)
            logger.info(f"{notebook.uuid} recover billing response: {rep}")
            if rep and isinstance(rep, dict) and rep.get("ret_code") == 0:
                notebook.charge_status = "active"
                await update_model(notebook)

        if notebook.charge_status == "active" and notebook.status in ["Suspending","Suspended"]:
            await notebook.async_suspend_billing()
            notebook.charge_status = "suspended"
            await update_model(notebook)

        if notebook.status == k8s_notebook_status.status and notebook.reason == k8s_notebook_status.reason:
            logger.info(f"Notebook/{notebook.uuid} status is {k8s_notebook_status.status}, no need to update")
            return

        logger.info(
            f"Notebook/{notebook.uuid} status [{notebook.status} -> {k8s_notebook_status.status}]. "
            f"reason [{notebook.reason} -> {k8s_notebook_status.reason}]"
        )

        try:
            # sync billing order
            if k8s_notebook_status.status == STATUS_PHASE.Running:
                if running_operation_record is None:
                    try:
                        notebook.lease()
                        notebook.charge_status = "active"
                    except Exception as e:
                        logger.error(f"Notebook/{notebook.uuid} lease error: {e}, created failed")
                        k8s_notebook_status.status = STATUS_PHASE.CreateFailed
                        k8s_notebook_status.reason = "订单创建失败"
                else:
                    logger.info(f"Notebook/{notebook.uuid} is Running, check replica and restart billing")
                    if not notebook.charge_status or notebook.charge_status in ["suspended", "suspending"]:
                        rep = await notebook.async_restart_billing(recover=True)
                        logger.info(f"{notebook.uuid} recover billing response: {rep}")
                        if rep and isinstance(rep, dict) and rep.get("ret_code") == 0:
                            notebook.charge_status = "active"
                            await update_model(notebook)
            elif k8s_notebook_status.status in [STATUS_PHASE.Suspended, STATUS_PHASE.Suspending]:
                logger.info(f"Notebook/{notebook.uuid} is Suspend(ing/ed), check replica and suspend billing")
                await notebook.async_suspend_billing()
                notebook.charge_status = "suspended"
                await update_model(notebook)
        except Exception as e:
            logger.error(f"Notebook/{notebook.uuid} restart billing error: {e}")

        if k8s_notebook_status.reason == NotebookReason.pulling_image_failed and NOTEBOOK_PRE_IMAGE_INFO_KEY in notebook_obj["metadata"]["annotations"]:
            is_starting = RedisClient().get(notebook.uuid)
            if is_starting:
                k8s_notebook_status.status = NotebookStatus.Creating if running_operation_record is None else NotebookStatus.Restarting
                k8s_notebook_status.reason = NotebookReason.pulling_image
            else:
                RedisClient().set(notebook.uuid, "starting", ex=3600*3)
                # 拉取镜像失败, 但是有镜像信息, 需要先同步镜像
                source_host, source_path = notebook_obj["metadata"]["annotations"][NOTEBOOK_PRE_IMAGE_INFO_KEY].split("|")
                source_ip, _ = await get_node_ip_and_runtime(source_host)
                pod_cr = await Pod.get(f"{notebook.uuid}-0", namespace=notebook.namespace)
                dest_host, dest_path = pod_cr.spec.nodeName, source_path
                dest_ip, dest_runtime = await get_node_ip_and_runtime(dest_host)
                task_id = transfer_image_tasks.gen_task_id(source_ip, dest_ip, dest_host, dest_path)
                task_info = transfer_image_tasks.get_task(task_id)
                if task_info is None:
                    # create transfer task
                    async_pool.submit(transfer_image_tasks.transfer_and_load_image, source_ip, dest_ip, source_path, dest_path, dest_runtime)
                    k8s_notebook_status.status = NotebookStatus.Creating if running_operation_record is None else NotebookStatus.Restarting
                    k8s_notebook_status.reason = NotebookReason.pulling_image
                else:
                    # check task status
                    if task_info["status"] in [TransferImageStatus.Running, TransferImageStatus.Success]:
                        k8s_notebook_status.status = NotebookStatus.Creating if running_operation_record is None else NotebookStatus.Restarting
                        k8s_notebook_status.reason = NotebookReason.pulling_image

                    if task_info["status"] in [TransferImageStatus.Failed, TransferImageStatus.Success]:
                        transfer_image_tasks.del_task(task_id)

        notebook.status, notebook.reason = k8s_notebook_status.status, k8s_notebook_status.reason
        if action == "MODIFIED" and k8s_notebook_status.status == NotebookStatus.Running:
            # 如果申请了ib, 获取pod信息, 创建ufm
            if app.settings.UFM_ENABLE and notebook.replica_specs.custom_infiniband > 0:
                pod_cr = await Pod.get(f"{notebook.uuid}-0", namespace=notebook.namespace)
                await create_ufm_info(notebook.user_id, [pod_cr.spec.nodeName])
        if k8s_notebook_status.status == NotebookStatus.Running:
            RedisClient().delete(notebook.uuid)
        if notebook.reason == NotebookReason.unschedulable or notebook.status == NotebookStatus.CreateFailed:
            # Creating状态下非 PVC导致的Unschedulable状态，直接设置为CreateFailed
            if running_operation_record is None:
                # 首次创建失败，设置自动删除时间
                notebook.status = STATUS_PHASE.CreateFailed
                notebook.auto_delete_time = datetime.datetime.now() + datetime.timedelta(hours=1)
            else:
                # 非首次创建失败，设置为Suspend
                notebook.status = STATUS_PHASE.Suspending

            # 在创建过程中调度失败或者Image拉取失败都关机
            nb = await NoteBookObj.get(notebook.uuid, namespace=notebook.namespace)
            await nb.shut_down()

        await OperationRecord.create_by_resource(notebook).save_async()
        await update_model(notebook)


def get_starting_numeric_part(input_string):
    match = re.match(r'^(\d+)', input_string)
    if match:
        return match.group(1)
    else:
        return 0


def cover_to_gb(input_string):
    match = re.match(r'^(\d+)', input_string)
    if match:
        values = match.group(1)
        if input_string[-2:].upper() == "KI":
            return int(values) / 1024 / 1024
        elif input_string[-2:].upper() == "MI":
            return int(values) / 1024
        elif input_string[-2:].upper() == "GI":
            return int(values)
        else:
            return int(values) / 1024 / 1024 / 1024
    else:
        return 0


async def update_node_status(node_info):
    async with get_async_session() as session:
        resource_node = await session.get(ResourceNodeStatus, node_info.metadata.name)
        if not resource_node:
            resource_node = ResourceNodeStatus()
        no_schedule = "NoSchedule"
        is_no_schedule = False

        taints: List[V1Taint]
        taints = node_info.spec.taints
        if hasattr(node_info.spec, "taints") and taints:
            for taint in taints:
                if taint.key == "node.kubernetes.io/unschedulable" and taint.effect == no_schedule:
                    is_no_schedule = True
                    break

        conditions: List[V1NodeCondition] = node_info.status.conditions
        for condition in conditions:
            if condition.status == "True":
                resource_node.status = condition.type

        if is_no_schedule:
            resource_node.status = no_schedule
        resource_node.uid = node_info.metadata.uid
        resource_node.node_id = node_info.metadata.name
        resource_node.total_cpu = int(get_starting_numeric_part(node_info.status.allocatable["cpu"])) / 1000
        if not resource_node.ava_gpu:
            resource_node.ava_cpu = resource_node.total_cpu
        resource_node.total_memory = cover_to_gb(node_info.status.allocatable["memory"])
        if not resource_node.ava_memory:
            resource_node.ava_memory = resource_node.total_memory

        total_gpu = 0
        resource_node.ib_enabled = 0
        resource_node.gpu_product = "-"
        logger.info("node status allocatable type [%s]", type(node_info.status.allocatable))
        logger.info("node status allocatable [%s]", node_info.status.allocatable)
        if "nvidia.com/gpu" in node_info.status.allocatable:
            total_gpu = int(node_info.status.allocatable["nvidia.com/gpu"])
            resource_node.gpu_product = NVIDIA
        elif "hexaflake/gpu" in node_info.status.allocatable:
            total_gpu = int(node_info.status.allocatable["hexaflake/gpu"])
            resource_node.gpu_product = HEXAFLAKE
        elif "huawei.com/Ascend910" in node_info.status.allocatable:
            total_gpu = int(node_info.status.allocatable["huawei.com/Ascend910"])
            resource_node.gpu_product = HUAWEI
        elif "hygon.com/dcu" in node_info.status.allocatable:
            total_gpu = int(node_info.status.allocatable["hygon.com/dcu"])
            resource_node.gpu_product = HYGON
        elif "huawei.com/Ascend310P" in node_info.status.allocatable:
            total_gpu = int(node_info.status.allocatable["huawei.com/Ascend310P"])
            resource_node.gpu_product = HUAWEI
        if "nvidia.com/hostdev" in node_info.status.allocatable:
            resource_node.ib_enabled = 1
        resource_node.total_gpu = total_gpu
        if not resource_node.ava_gpu:
            resource_node.ava_gpu = resource_node.total_gpu
        if resource_node.total_gpu > 0:
            if node_info.metadata.labels.get("aicp.group/aipods_type") in app.settings.VGPU_TAG:
                resource_node.worker_node_type = "vgpu"
            else:
                resource_node.worker_node_type = "gpu"
        session.add(resource_node)
        await session.commit()
        logger.info(f"update node status: {node_info.metadata.name} done")


async def timer_update_node_status(interval=60):
    await update_node_static_info()
    while True:
        await asyncio.sleep(interval)
        data_disk_mount_path = app.settings.DOCKER_DATA_DISK
        os_disk_mount_path = app.settings.DOCKER_OS_DISK
        rep1 = await PromethusAsyncClient().get_disk_ava(os_disk_mount_path)
        rep2 = await PromethusAsyncClient().get_disk_total(os_disk_mount_path)
        rep3 = await PromethusAsyncClient().get_disk_ava(data_disk_mount_path)
        rep4 = await PromethusAsyncClient().get_disk_total(data_disk_mount_path)
        async with get_async_session() as session:
            for node in rep1:
                node_id = node["metric"]["instance"]
                resource_node = await session.get(ResourceNodeStatus, node_id)
                resource_node.ava_os_disk = int(float(node["value"][1]))
                session.add(resource_node)
            for node in rep2:
                node_id = node["metric"]["instance"]
                resource_node = await session.get(ResourceNodeStatus, node_id)
                resource_node.total_os_disk = int(float(node["value"][1]))
                session.add(resource_node)
            for node in rep3:
                node_id = node["metric"]["instance"]
                resource_node = await session.get(ResourceNodeStatus, node_id)
                resource_node.ava_data_disk = int(float(node["value"][1]))
                session.add(resource_node)
            for node in rep4:
                node_id = node["metric"]["instance"]
                resource_node = await session.get(ResourceNodeStatus, node_id)
                resource_node.total_data_disk = int(float(node["value"][1]))
                session.add(resource_node)
            r = await session.exec(select(ResourceNodeStatus))
            node_list = r.fetchall()
            k8s_node: List[Node] = await Node.list()
            k8s_node_names = []
            for n in k8s_node:
                k8s_node_names.append(n.metadata.name)
            for resource_node in node_list:
                if resource_node.node_id not in k8s_node_names:
                    await session.delete(resource_node)
                    r = await session.exec(
                        select(NodeStaticInfo).where(NodeStaticInfo.hostname == resource_node.node_id))
                    node_static_info = r.one_or_none()
                    if node_static_info:
                        await session.delete(node_static_info)
                    continue
                data = await PromethusAsyncClient().get_resource_requests(resource_node.node_id)
                # resource_node = await session.get(ResourceNodeStatus, node_id)
                used_gpu = 0
                for node in data:
                    resource_name = node["metric"]["resource"]
                    if resource_name == "cpu":
                        resource_node.ava_cpu = resource_node.total_cpu - int(float(node["value"][1]))
                    if resource_name == "memory":
                        resource_node.ava_memory = resource_node.total_memory - int(
                            float(node["value"][1])) / 1024 / 1024 / 1024
                    gpu_matcher = gpu_manager.get_gpu_matcher_by_resource_rfc_1123(resource_name)
                    if gpu_matcher is not None:
                        logger.debug(f"gpu_matcher: {gpu_matcher} : {node['value']}")
                        used_gpu = int(float(node["value"][1]))
                    ava_gpu = resource_node.total_gpu - used_gpu
                    resource_node.ava_gpu = ava_gpu
                    session.add(resource_node)
                await session.commit()
            logger.info(f"Timer fired after {interval} seconds")


async def update_node_static_info():
    nodes: List[Node] = await Node.list()
    async with get_async_session() as session:
        for node in nodes:
            try:
                labels = node.metadata.labels
                role = []
                if "node-role.kubernetes.io/master" in labels:
                    role.append("master")
                if "node-role.kubernetes.io/worker" in labels:
                    role.append("worker")
                if "node-role.kubernetes.io/control-plane" in labels:
                    role.append("control-plane")
                gpu_model = '-'
                gpu_memory = 0
                gpu = 0
                ib_count_compute = 0
                ib_bw_compute = 0
                ib_count_storage = 0
                ib_bw_storage = 0
                ib_bw_manager = 0
                ib_count_manager = 0
                nvlink = 0
                node_addr = node.status.addresses[0]["address"]
                node_type = await check_gpu_type(node_addr)
                logger.info("get node type [%s]", node_type)
                if node_type in gpu_handle_map:
                    gpu_infos = await gpu_handle_map[node_type](node_addr)
                else:
                    gpu_infos = []
                logger.info("gety node gpu info [%s]", gpu_infos)
                cpu_model = await check_cpu_type(node_addr)
                overlay2_size = await get_host_docker_overlay2_size(node_addr)
                index = 0
                for gpu_info in gpu_infos:
                    if gpu_info:
                        gpu_uuid = gpu_info["uuid"]
                        gpu = len(gpu_infos)
                        gpu_static_info = await session.get(GpuStaticInfo, gpu_uuid)
                        if not gpu_static_info:
                            gpu_static_info = GpuStaticInfo()
                        gpu_static_info.index = index
                        gpu_static_info.gpu_uuid = gpu_uuid
                        gpu_static_info.hostname = node.metadata.name
                        gpu_memory = gpu_info["memory"]
                        gpu_static_info.gpu_memory = gpu_memory
                        gpu_static_info.gpu_model = gpu_info["gpu_model"]
                        gpu_model = gpu_info["gpu_model"]
                        logger.info("add gpu static info [%s]", gpu_static_info)
                        session.add(gpu_static_info)
                    index = index + 1
                ib_infos = await get_ib_dev_info(node_addr)
                nic_infos = await get_nic_info(node_addr)
                rate_list = {}
                for ib_info in ib_infos:
                    ca = ib_info["ca"]
                    guid = ib_info["guid"]
                    rate = ib_info["rate"]
                    rate_list.update({
                        guid: rate
                    })
                    ib_dev_info = await session.get(IbDevInfo, guid)
                    if not ib_dev_info:
                        ib_dev_info = IbDevInfo()
                    ib_dev_info.dev_name = ca
                    ib_dev_info.guid = guid
                    ib_dev_info.ib_bw = int(rate)
                    ib_dev_info.hostname = node.metadata.name
                    session.add(ib_dev_info)
                for item in nic_infos:
                    if item['ifname'].startswith(("lo", "docker", "vnet", "br_vg_out", "kube", "vg_tep_out", "tunl", "cali")):
                        continue
                    if item["link_type"] == 'infiniband':
                        nic_mac = "0x" + item['address'][36:].replace(":", "")
                        rate = rate_list.get(nic_mac)
                        rate_list.pop(nic_mac)
                        if item["addr_info"]:
                            if rate <= 25:
                                ib_count_manager += 1
                                ib_bw_manager = max(rate, ib_bw_manager)
                            else:
                                ib_count_storage += 1
                                ib_bw_storage = max(rate, ib_bw_storage)
                        else:
                            ib_count_compute += 1
                            ib_bw_compute = max(rate, ib_bw_compute)
                if rate_list:
                    for item in rate_list:
                        ib_count_manager += 1
                        ib_bw_manager = max(rate_list.get(item), ib_bw_manager)
                node_static_info = await session.get(NodeStaticInfo, node.metadata.name)
                if not node_static_info:
                    node_static_info = NodeStaticInfo()
                node_static_info.cpu_model = cpu_model
                node_static_info.overlay2_size = overlay2_size
                node_static_info.ib_count_compute = ib_count_compute
                node_static_info.ib_bw_compute = ib_bw_compute
                node_static_info.ib_bw_storage = ib_bw_storage
                node_static_info.ib_count_storage = ib_count_storage
                node_static_info.ib_bw_manager = ib_bw_manager
                node_static_info.ib_count_manager = ib_count_manager
                node_static_info.nvlink = nvlink
                node_static_info.role = ",".join(role)
                node_static_info.hostname = node.metadata.name
                node_static_info.ip = node_addr
                node_static_info.gpu_memory = gpu_memory
                node_static_info.gpu_model = gpu_model
                node_static_info.cpu = int(node.status.capacity.cpu)
                node_static_info.memory = int(
                    get_starting_numeric_part(node.status.capacity.memory)) // 1024 // 1024
                node_static_info.gpu = gpu
                session.add(node_static_info)
            except Exception as e:
                logger.error(e)
                logger.error(traceback.format_exc())
        await session.commit()
        logger.info(f"update node static info done")


async_bulk_queue = asyncio.Queue()


async def bulk_watch_items():
    """
    bulk watch items to elasticsearch
    """
    bulk_data = []
    pre_submit_time = datetime.datetime.now()

    # submit bulk data when, satisfy one of the following conditions:
    # 1. 5 seconds passed
    # 2. bulk_data size > 100
    # 3. bulk_data size > 1MB
    while True:
        try:
            now = datetime.datetime.now()
            if async_bulk_queue.empty():
                if bulk_data and (now - pre_submit_time).seconds > 5:
                    logger.info(f"submit bulk data, size: {len(bulk_data)}, because of timeout")
                    await async_bulk(opensearch_client, bulk_data)
                    bulk_data.clear()
                    pre_submit_time = now
                await asyncio.sleep(1)
            else:
                obj = async_bulk_queue.get_nowait()
                obj.update({
                    "@timestamp": datetime.datetime.now(),
                    '_op_type': 'index',
                    '_index': 'kube_resource',
                    '_id': obj["metadata"]["uid"],
                })
                bulk_data.append(obj)
                if len(bulk_data) >= 200 or sys.getsizeof(bulk_data) > 1024 * 1024:
                    logger.info(f"submit bulk data, size: {len(bulk_data)}, because of size")
                    await async_bulk(opensearch_client, bulk_data)
                    bulk_data.clear()
                    pre_submit_time = now
        except Exception as e:
            logger.exception(f"bulk watch items error: {e}")
            bulk_data.clear()


official_kind_list_func = {
    "Pod": "list_pod_for_all_namespaces",
    "Node": "list_node",
    "Service": "list_service_for_all_namespaces",
}


async def async_watch_official_kind(kind: str):
    logger.info(f"watching {kind}")
    last_seen_version = ''
    index = 0
    while True:
        try:
            async with client.ApiClient() as api:
                v1 = client.CoreV1Api(api)
                async with watch.Watch().stream(getattr(v1, official_kind_list_func[kind]),
                                                resource_version=last_seen_version) as stream:
                    async for event in stream:
                        index += 1
                        evt, obj, raw = event["type"], event["object"], event["raw_object"]
                        if evt == "DELETED":
                            continue

                        if last_seen_version == '' or (
                                int(raw["metadata"]["resourceVersion"]) > int(last_seen_version)):
                            last_seen_version = raw["metadata"]["resourceVersion"]
                        await async_bulk_queue.put(remove_dot_key(raw))
                        if kind == "Node":
                            async_pool.submit(update_node_status, obj)
        except ApiException as e:
            last_seen_version = ""
            if e.status == 410:
                logger.error(f"Reason: Expired: too old resource version: {last_seen_version} : {e}")
            else:
                logger.exception(f"watch {kind} error: {e}")
        except Exception as e:
            logger.exception(f"watch {kind} error: {e}")


async def async_watch_objects(resource_kind: APIObject):
    last_seen_version = ''
    while True:
        logger.info(f"watching {resource_kind.kind}")
        if resource_kind.endpoint == NoteBookObj.endpoint:
            async_pool.submit(update_invalid_notebook_status)
        elif resource_kind.endpoint in ALL_ENDPOINTS:
            async_pool.submit(update_invalid_train_status, resource_kind.endpoint)

        try:
            async with client.ApiClient() as api:
                v1 = client.CustomObjectsApi(api)
                async with watch.Watch().stream(
                        v1.list_cluster_custom_object,
                        group=resource_kind.version.split("/")[0], version=resource_kind.version.split("/")[1],
                        plural=resource_kind.plural, resource_version=last_seen_version
                ) as stream:
                    async for event in stream:
                        try:
                            action, obj = event["type"], event["raw_object"]
                            if last_seen_version == '' or (
                                    int(obj["metadata"]["resourceVersion"]) > int(last_seen_version)):
                                last_seen_version = obj["metadata"]["resourceVersion"]
                            logger.info(
                                f"{action} {obj['kind']}/{obj['metadata']['namespace']}/{obj['metadata']['name']}/{obj['metadata']['resourceVersion']}")

                            if action != "DELETED":
                                await async_bulk_queue.put(remove_dot_key(obj))

                            # 更新示例最新的version
                            instance_versions.update_latest_version(obj)

                            if resource_kind.endpoint in ALL_ENDPOINTS and obj.get("status", []):
                                async_pool.submit(update_train_status, action, obj)
                            elif resource_kind.endpoint == NoteBookObj.endpoint:
                                async_pool.submit(update_notebook_status, action, obj)
                        except Exception as e:
                            logger.exception(f"watch {resource_kind.kind}: [{event}] error: {e}")
        except ApiException as e:
            last_seen_version = ""
            if e.status == 410:
                logger.error(f"Reason: Expired: too old resource version: {last_seen_version} : {e}")
            else:
                logger.exception(f"watch {resource_kind.kind} error: {e}")
        except Exception as e:
            logger.exception(f"watch {resource_kind.kind} error: {e}")

        logger.info(f"{resource_kind.kind} pre watch is closed, retrying")
        await asyncio.sleep(5)


def generate_id() -> str:
    data_id = "op-" + generate_random_string() + str(time.time_ns())
    return data_id



async def clear_node():
    while True:
        try:
            async with get_async_session() as session:
                stmt = select(ResourceNode).where(ResourceNode.status==STATUS_SUSPENDED)
                cur = await session.exec(stmt)
                resource_nodes = cur.all()
                for resource_node in resource_nodes:
                    user_id = resource_node.user_id
                    nodes: Union[APIObject, List[APIObject]] = await Node.list(label_selector=f"{RG_NODE_TAG_KEY}={resource_node.rg_node_id}")
                    if len(nodes) == 0:
                        logger.info(f"Node {resource_node.rg_node_id} deleted")
                    else:
                        for node in nodes:
                            taints = node.spec.taints
                            delete_node = False
                            for taint in taints:
                                if taint.key == RG_NODE_TOLERATION_KEY_SUSPENDED:
                                    try:
                                        dt_obj = datetime.datetime.strptime(taint.value, "%Y%m%d%H%M%S")
                                        delta  = datetime.datetime.now() - dt_obj
                                        if delta.total_seconds() > app.settings.delete_node_delay_time:
                                            delete_node = True
                                    except Exception as e:
                                        logger.error(f"parse datetime error: {e}")
                                        delete_node = True
                            if delete_node:
                                logger.info(f"resource node  {node.name} will be remove")
                                pods: Union[APIObject, List[APIObject]] = await Pod.list(namespace=user_id.lower(), field_selector=f"spec.nodeName={node.name}")
                                logger.info(f"get pod on node {node.name} info {pods}")
                                for pod in pods:
                                    logger.info(f"get pod {pod.name}")
                                    namespace = pod.metadata.namespace
                                    if namespace == user_id.lower():
                                        logger.info(f"delete namespace pod {pod.name}")
                                        await Pod.delete(pod)
                                local_session = await asyncio.to_thread(get_db_session_local)
                                rg_db = ResourceGroupCrud(session=local_session)
                                await asyncio.to_thread(rg_db.delete_rgn_by_rg_node_ids,[resource_node.rg_node_id], user_id)
                                logger.info(f"delete resource node  {node.name}")
                                await asyncio.to_thread(local_session.close)
            await asyncio.sleep(600)
        except Exception as e:
            logger.exception(f"check note status error: {e}")



def main():
    async_pool.submit(load_kube_config)
    async_pool.wait()

    async_pool.submit(bulk_watch_items)

    endpoints = [NoteBookObj] + ALL_TRAINS
    # register watch objects
    list([async_pool.submit(async_watch_objects, endpoint) for endpoint in endpoints])
    # watch official kind
    list([async_pool.submit(async_watch_official_kind, endpoint) for endpoint in ["Pod", "Node", "Service"]])

    async_pool.submit(timer_update_node_status, 180)
    async_pool.submit(auto_stop_notebook)
    async_pool.submit(auto_delete_notebook)
    async_pool.submit(clear_node)
    if app.settings.IAAS_MESSAGE_ENABLE:
        async_pool.submit(send_stop_message)
        async_pool.submit(send_delete_message)
    async_pool.wait()


if __name__ == '__main__':
    # notify
    watch_thread = threading.Thread(target=watch_config_file, args=())
    watch_thread.daemon = True
    watch_thread.start()
    main()
