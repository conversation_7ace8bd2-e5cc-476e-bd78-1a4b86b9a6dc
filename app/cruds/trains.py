import csv
import io
import json
import math
import os
import asyncio
import uuid
from collections import defaultdict
from datetime import datetime, timedelta
from statistics import mean
from typing import DefaultDict, Iterable, List, Sequence, Tuple
from uuid import UUID

import kr8s
from fastapi import status as http_status
from httpx import HTTPStatusError
from kr8s import NotFoundError
from kr8s.objects import APIObject
from sqlalchemy import <PERSON>alarR<PERSON>ult
from sqlalchemy.exc import OperationalError
from sqlalchemy.orm import selectinload
from sqlmodel import Session, desc, or_, select
import app
from app import logger
from app.core.exceptions import PermissionDeniedException, ResourceNotFoundException
from app.core.models import ActiveRecordMixin, QingcloudUser, VolumeSpecCreate, EnvModel
from app.core.kube.api.dockercfg import DockerCfgSecret
from app.core.qingcloud.common import get_qingcloud_user_info_by_user_id
from app.core.volumes.manager import VolumesManager
from app.core.opensearch.client import es_client
from app.apps.trains.exceptions import Train<PERSON>reateEx<PERSON>, TrainDeleteException, \
    TrainNameAlreadyExistsException, TrainPatchException, VolumeCreateException, TrainPriorityStatusException, \
    TrainRestartDenied
from app.apps.trains.kr8s_objects import BaseJob
from app.apps.trains.kubernetes import TrainsK8sManager
from app.models.operation_record import OperationRecord
from app.cruds.task import TaskService
from app.models.trains import ALL_SEARCHED_TRAIN_STATUS, ExportRecord, Train, TrainCreate, TrainPatch, TrainRead, TrainReason, \
    TrainReplicaSpec, TrainStatus, \
    TrainVolumeSpec, export_headers, train_status_zhCN
from app.apps.trains.query import DownloadExportQueryParams, TrainSearchQuaryParams
from app.models.gpu import NodeStaticInfo
from app.cruds.resource_group import ResourceGroupCrud
from app.models.resource_group import ResourceGroup, ResourceNode, ResourceTemplate
from app.core.config import LocalPathStorageMount
from app.core.constant import PRIORITY_CLASSES_JOBS
from app.core.kube.api import STATUS_PHASE, list_pods
from app.core.prometheus.monitoring_operator import MonitoringOperator
from app.core.prometheus.query_params import MonitoringQueryParams
from app.core.qingcloud.interface import describe_users, patch_user_info_for_models, ZONE_INFO, send_message_request, \
    RELEASE_RESOURCE
from app.core.utils import convert_seconds_to_hour, convert_seconds_to_h_m_s, get_tmp_file_full_path, split_list, \
    strftime, to_persent_str
from app.core.qingcloud.interface import describe_users, patch_user_info_for_models
from app.core.utils import convert_seconds_to_hour, convert_seconds_to_h_m_s, log_background_task_exception, split_list, strftime, to_persent_str


class TrainCRUD:
    """
    Train CRUD
    """

    def __init__(self, session: Session, user: QingcloudUser = None):
        self.session = session
        self.user = user

    def get_by_uuid(self, uuid: str) -> Train:
        """
        通过uuid获取作业
        :param uuid:
        :return:
        """
        stmt = select(Train).where(Train.uuid == uuid)
        train_res: ScalarResult[Train] = self.session.scalars(stmt)
        train: Train = train_res.first()
        if not train:
            raise ResourceNotFoundException(message=uuid)
        return train

    def get_by_uuids(self, uuids: List[str]) -> Sequence[Train]:
        """
        通过uuid获取作业
        :param uuid:
        :return:
        """
        stmt = select(Train).where(Train.uuid.in_(uuids))
        train_res: ScalarResult[Train] = self.session.scalars(stmt)
        trains: Sequence[Train] = train_res.fetchall()
        return trains

    def get_by_uuid_with_permission(self, uuid: str) -> Train:
        """
        通过uuid获取作业
        :param uuid:
        :return:
        """
        stmt = select(Train).where(Train.uuid == uuid)
        if not self.user.is_super_user():
            stmt = stmt.where(Train.user_id == self.user.user_id)
        train_res: ScalarResult[Train] = self.session.scalars(stmt)
        train: Train = train_res.first()
        if not train:
            raise PermissionDeniedException(message=uuid)
        return train

    def get_by_uuids_with_permission(self, uuids: List[str]) -> Sequence[Train]:
        """
        通过uuid获取作业
        :param uuid:
        :return:
        """
        stmt = select(Train).where(Train.uuid.in_(uuids))
        if not self.user.is_super_user():
            stmt = stmt.where(Train.user_id == self.user.user_id)
        train_res: ScalarResult[Train] = self.session.scalars(stmt)
        trains: Sequence[Train] = train_res.fetchall()
        return trains

    def create(self, data: TrainCreate, uuid=None) -> Train:
        """
        创建作业
        :param data:
        :return:
        """

        train = Train(**data.dict(exclude={"volume_specs", "replica_specs"}), status=TrainStatus.Creating)
        if uuid:
            train.uuid = uuid
        for spec in data.replica_specs:
            rgn = spec.specs
            # 如果使用获取资源规则
            if spec.template_id:
                template = ResourceTemplate.one_by_id(spec.template_id, session_=self.session)
                spec.sqlmodel_update(template.to_spec())
                if not spec.specs:
                    spec.specs = rgn
            if spec.rg_id is not None:
                # 检查专属资源组权限
                resource: ResourceNode = ResourceGroupCrud(self.session).get_resource_group_node_by_user_id(
                    spec.rg_id, spec.specs, self.user.user_id, gpu_name=spec.custom_gpu_name)
                if spec.custom_gpu:
                    spec.custom_gpu_type = resource.gpu_model
                    spec.custom_gpu_name = resource.gpu_name
                    if spec.custom_gpu == resource.gpu:
                        node_static: NodeStaticInfo = NodeStaticInfo.one_by_id(resource.hostname, session_=self.session)
                        spec.custom_infiniband = node_static.ib_count_compute

            replica_spec = TrainReplicaSpec(**spec.dict())
            replica_spec.train = train
            replica_spec.update_custom_specs()
            train.replica_specs.append(replica_spec)
            replica_spec.resource.lease(train.uuid, self.user.user_id, charge_mode=data.charge_mode,
                                        duration=data.duration)

        # 添加SharedMemoryVolume
        data.volume_specs.append(
            VolumeSpecCreate(file_set=f"/dev/shm", mount_path="/dev/shm", volume_type="SHARED_MEMORY",
                             quota=math.ceil(train.replica_specs[0].resource.memory.value / 2))
        )
        data.volume_specs.append(
            VolumeSpecCreate(
                file_set=app.settings.POD_MODEL_NAME_MOUNT_PATH,
                mount_path=app.settings.HOST_MODEL_NAME_MOUNT_PATH,
                volume_type="HOST_PATH",
                permission="ro",
            )
        )

        #HYGON(DCU) 资源添加 /opt/hyhal
        if train.get_gpu_vendor() == "HYGON":
            data.volume_specs.append(
                VolumeSpecCreate(file_set="/opt/hyhal", mount_path="/opt/hyhal", volume_type="HOST_PATH", quota=1,
                                 permission='ro')
            )
        for paths in app.settings.LOCAL_PATH_STORAGE_MOUNT_PATHS:
            # 兼容LOCAL_PATH_STORAGE_MOUNT_PATHS为list和dict两种格式
            permission = "rw"
            mount_path = paths
            if isinstance(app.settings.LOCAL_PATH_STORAGE_MOUNT_PATHS, dict):
                mount_path = app.settings.LOCAL_PATH_STORAGE_MOUNT_PATHS[paths]
            elif isinstance(app.settings.LOCAL_PATH_STORAGE_MOUNT_PATHS, list):
                if isinstance(paths, LocalPathStorageMount):
                    mount_path = paths.MOUNT_PATH
                    permission = paths.PERMISSION
                    paths = paths.HOST_PATH

            data.volume_specs.append(
                VolumeSpecCreate(file_set=paths, mount_path=mount_path, volume_type="LOCAL_PATH", permission=permission)
            )

        # 挂载存储
        for volume_spec in data.volume_specs:
            vm = VolumesManager(self.user, data.namespace, volume_spec)
            try:
                vm.create_storage()
                vs: TrainVolumeSpec = TrainVolumeSpec(**volume_spec.dict())
                train.volume_specs.append(vs)
                vs.pvc_name = vm.volume.pvc_name
            except Exception as e:
                logger.exception(f"create volume failed : {e}")
                logger.error(f"create volume failed : {e}")
                # vm.delete_storage()
                raise VolumeCreateException(volume_spec.file_set)

        docker_secret_name = None
        if data.image_type == "custom" and data.custom_image_secret.has_auth:
            docker_secret = DockerCfgSecret(data.namespace, data.image.split("/")[0],
                                            data.custom_image_secret.username, data.custom_image_secret.password)
            docker_secret.create()
            docker_secret_name = docker_secret.name

        # get k8s job object
        job_object: APIObject = TrainsK8sManager(train, self.user, docker_secret_name).get_resource_definition()

        try:
            self.session.add(train)
            job_object.create()
            train.kuid = job_object.metadata.uid
            es_client.insert_or_update(_id=job_object.metadata.uid,
                                       body=job_object.raw)

            self.session.commit()
            self.session.refresh(train)
            return train
        except HTTPStatusError as e:
            self.session.rollback()
            logger.error(f"create trains error: {e.response.text}")
            if e.response.status_code == http_status.HTTP_409_CONFLICT:
                logger.warning(f"train already exists: {e.response.text}")
                raise TrainNameAlreadyExistsException(data.name)
            raise TrainCreateException(data.name)
        except OperationalError as e:
            logger.error(f"create trains error: {e}")
            job_object.delete()
            raise TrainCreateException(data.name)
        except Exception as e:
            logger.error(f"create trains error: {e}")
            try:
                job_object.delete()
            except NotFoundError:
                pass
            raise TrainCreateException(data.name)

    def terminated(self, uuid: str, reason="", force=False):
        """
        停止作业
        only used by billing so don't check permission
        :param uuid:
        :param user_id:
        :return:
        """
        stmt = select(Train).where(Train.uuid == uuid)
        train: Train = self.session.exec(stmt).first()
        if not train:
            logger.warning(f"train not found: {uuid} and {self.user.user_id}")
            raise PermissionDeniedException(message=uuid)

        if train.status != TrainStatus.Deleted:
            train.status = TrainStatus.Terminated
        train.ended_at = datetime.now()
        train.reason = reason
        OperationRecord.create_by_resource(train, user_id="Billing").save(session_=self.session)
        self.session.commit()

        # select k8s by meta uuid
        train.replica_specs[0].resource.unlease(train.uuid, self.user.user_id)
        try:
            train_object = kr8s.get(train.endpoint, train.uuid, namespace=train.namespace)
            if train_object:
                train_object[0].delete()
                # IAAS send message.
                user_info = get_qingcloud_user_info_by_user_id(train.user_id)
                send_data = {
                    "username": user_info.get("user_name"),
                    "zone": ZONE_INFO,
                    "name": train.name,
                    "resource": "训练任务",
                    "id": train.uuid
                }
                send_message_request(RELEASE_RESOURCE, train.user_id, json.dumps(send_data))
        except HTTPStatusError as e:
            logger.error(f"delete trains error: {e.response.text}")
            raise TrainDeleteException(message=uuid)
        finally:
            self.session.refresh(train)

        return train

    def delete(self, uuid):
        """
        删除作业
        :param uuid:
        :return:
        """
        logger.info(f"delete train: {uuid}")
        train: Train = Train.one_by_id(uuid, session_=self.session)

        try:
            train.status = TrainStatus.Deleted
            train.replica_specs[0].resource.unlease(train.uuid, train.user_id)
            self.session.commit()
            try:
                # select k8s by meta uuid
                train_object = kr8s.get(train.endpoint, train.uuid, namespace=train.namespace)
                if train_object:
                    train_object[0].delete()
            except NotFoundError as e:
                logger.info("Not found k8s object")

        except HTTPStatusError as e:
            logger.error(f"delete trains error: {e.response.text}")
            raise TrainDeleteException(message=uuid)
        finally:
            self.session.refresh(train)

        return train

    def search(
            self, namespace: str, query: TrainSearchQuaryParams
    ) -> Tuple[int, Sequence[TrainRead]]:
        """
        查询作业
        :return:
        """
        statement = select(Train) \
            .options(selectinload(Train.volume_specs)) \
            .options(selectinload(Train.replica_specs)) \
            .offset(query.offset).limit(query.limit)

        if self.user.is_kse_admin_user():
            if query.owner:
                statement = statement.where(Train.user_id == query.owner)

        if namespace != "ALL":
            statement = statement.where(Train.namespace.in_(namespace.split(",")))

        if query.name is not None:
            statement = statement.where(or_(Train.name.contains(query.name), Train.uuid == query.name))
        if query.job_type:
            # statement = statement.where(Train.job_type == query.job_type)
            statement = statement.where(Train.job_type.in_(query.job_type))
        if query.endpoints is not None:
            statement = statement.where(Train.endpoint.in_(query.endpoints))

        query.status = query.status or ALL_SEARCHED_TRAIN_STATUS
        if query.status:
            statement = statement.where(Train.status.in_(query.status))

        if query.start_at is not None:
            statement = statement.where(Train.created_at >= query.start_at)

        if query.end_at is not None:
            statement = statement.where(Train.created_at <= query.end_at)

        if query.order_by is not None:
            order_by = desc(query.order_by) if query.reverse else query.order_by
            statement = statement.order_by(order_by)

        if query.project_category:
            statement = statement.where(Train.project_category.in_(query.project_category))

        count: int = Train.count_over_all_by_stmt(statement)
        trains: ScalarResult[Train] = self.session.scalars(statement)
        res: Sequence[Train] = trains.fetchall()

        train_reads = [TrainRead.from_orm(train) for train in res]
        patch_user_info_for_models(train_reads)

        rg_ids = list(
            set([replica.rg_id for train in train_reads for replica in train.train_replica_specs if replica.rg_id]))
        rgs = ResourceGroup.all_by_ids(rg_ids, session_=self.session)
        logger.debug(f"rgs: {rgs} with rg_ids: {rg_ids}")
        rg_infos = {rg.rg_id: rg for rg in rgs}

        for train in train_reads:
            for replica in train.train_replica_specs:
                rg = rg_infos.get(replica.rg_id)
                if rg:
                    replica.rg_name = rg.name

        return count, train_reads

    def get_presigned_put_object_url(self, namespace: str, uuid_c: UUID, path: str, ):
        """
        获取临时挂载路径上传文件的url
        :param namespace:
        :param uuid_c:
        :param path:
        :return:
        """
        raise RuntimeError("Deprecated")

    def get_train_by_uuid_and_user_id(self, uuid: str) -> TrainRead:
        """
        获取作业
        :param uuid:
        :return:
        """
        stmt = select(Train).where(Train.uuid == uuid) \
            .options(selectinload(Train.volume_specs)) \
            .options(selectinload(Train.replica_specs))

        if not self.user.is_super_user():
            stmt = stmt.where(Train.user_id == self.user.user_id)

        train_res: ScalarResult[Train] = self.session.scalars(stmt)
        train: Train = train_res.first()
        if not train:
            raise PermissionDeniedException(message=uuid)

        return TrainRead.from_orm(train)

    def get_train_by_uuid_with_permission(self, uuid: str):
        stmt = select(Train).where(Train.uuid == uuid) \
            .options(selectinload(Train.volume_specs)) \
            .options(selectinload(Train.replica_specs))

        if not self.user.is_super_user():
            stmt = stmt.where(Train.user_id == self.user.user_id)

        train_res: ScalarResult[Train] = self.session.scalars(stmt)
        train: Train = train_res.first()
        if not train:
            raise PermissionDeniedException(message=uuid)
        return train

    def update_train_priority(self, uuid: str, priority: int):
        """
        修改训练任务优先级
        :return:
        """
        train: Train = self.get_by_uuid(uuid)
        # 只有Pending状态的Pod才可以修改
        if train.status not in [STATUS_PHASE.Pending, STATUS_PHASE.Creating, STATUS_PHASE.Created,
                                STATUS_PHASE.Inqueuing]:
            raise TrainPriorityStatusException(f"当前状态:{train.status}")
        if train.priority == priority:
            logger.info("优先级相同，无需修改")
            return

        # 修改CR的priorityClass字段和数据库
        try:
            trainning_crs = kr8s.get(train.endpoint, train.uuid, namespace=train.namespace)
            trainning_cr: BaseJob = trainning_crs[0]
            print(PRIORITY_CLASSES_JOBS[priority - 1])
            trainning_cr.patch(
                [{"op": "replace", "path": "/spec/runPolicy/schedulingPolicy/priorityClass",
                  "value": PRIORITY_CLASSES_JOBS[priority - 1]}],
                type="json"
            )
            train.priority = priority
            self.session.commit()
        except HTTPStatusError as e:
            logger.error(f"patch the priority of train error: {e}")
            self.session.rollback()
            raise TrainPatchException(message=uuid)

    def patch_train(self, data: TrainPatch):
        """
        更新作业
        :param data:
        :return:
        """
        train: Train = Train.one_by_id(data.uuid, session_=self.session)

        if data.name:
            train.name = data.name

        if data.project_category:
            train.project_category = data.project_category

        if data.status and train.status != data.status:
            train.check_patch_status(data.status)
            try:
                train_object: APIObject = None
                train_objects = kr8s.get(train.endpoint, train.uuid, namespace=train.namespace)
                if train_objects:
                    train_object = train_objects[0]
                if data.status == TrainStatus.Terminated:
                    train.replica_specs[0].resource.unlease(train.uuid, train.user_id)
                    train.status = TrainStatus.Terminated
                    train.ended_at = datetime.now()
                    train.reason = "terminated by user"
                    self.session.commit()
                    if train_object:
                        train_object.delete()
                else:
                    val = True if data.status == TrainStatus.Suspended else False
                    train_object.patch(
                        [{"op": "replace", "path": "/spec/runPolicy/suspend", "value": val}],
                        type="json"
                    )
                    train.status = data.status
                    self.session.commit()
            except HTTPStatusError as e:
                logger.error(f"patch trains error: {e.response.text}")
                self.session.rollback()
                raise TrainPatchException(message=data.uuid)

            self.session.refresh(train)
        return train

    def suspended(self, uuid: str) -> Train:
        """
        暂停作业
        :param uuid:
        :return:
        """
        train: Train = Train.one_by_id(uuid, session_=self.session)
        train.suspend_billing()

        train_object: List[BaseJob] = kr8s.get(train.endpoint, train.uuid, namespace=train.namespace) # noqa
        train_object[0].suspend()
        train.update(status=TrainStatus.Suspended, reason=TrainReason.suspend_by_user, session_=self.session)
        OperationRecord.create_by_resource(train).insert()
        return train

    def restart(self, uuid: str) -> Train:
        """
        重启作业
        :param uuid:
        :return:
        """
        train: Train = Train.one_by_id(uuid, session_=self.session)
        job_pods = list_pods(self.user.user_id.lower(), f"app={uuid}")
        if len(job_pods.items) != 0:
            raise TrainRestartDenied()
        train.check_resource_balance()
        train_object: List[BaseJob] = kr8s.get(train.endpoint, train.uuid, namespace=train.namespace) # noqa
        train_object[0].restart()
        # 重启需要排队
        if app.settings.VOLCANO_ENABLE:
            train.update(status=TrainStatus.Inqueuing, reason=TrainReason.restarting, session_=self.session)
        else:
            train.update(status=TrainStatus.Restarting, reason=TrainReason.restarting, session_=self.session)
        OperationRecord.create_by_resource(train).insert()
        return train

    @log_background_task_exception
    def suspended_by_billing(self, uuid: str):
        """
        暂停作业, 异步后台作业, 此时的session已经关闭
        :param uuid:
        :return:
        """
        logger.info(f"Suspended Train by billing: {uuid}")
        train: Train = Train.one_by_id(uuid)
        if train.reason == TrainReason.suspend_by_billing:
            logger.info(f"Train {uuid} has been suspended by billing")
            return

        train.update(status=TrainStatus.Suspending, reason=TrainReason.suspend_by_billing)

        train_object: List[BaseJob] = kr8s.get(train.endpoint, train.uuid, namespace=train.namespace)
        train_object[0].suspend()

        train.update(status=TrainStatus.Suspended, reason=TrainReason.suspend_by_billing)
        OperationRecord.create_by_resource(train).insert()
        return train

    @log_background_task_exception
    def resume_by_billing(self, uuid: str):
        """
        恢复作业, 异步后台作业, 此时的session已经关闭
        :param uuid:
        :return:
        """
        train: Train = Train.one_by_id(uuid)

        train_object: List[BaseJob] = kr8s.get(train.endpoint, train.uuid, namespace=train.namespace) # noqa
        train_object[0].restart()

        train.update(status=TrainStatus.Restarting, reason=TrainReason.restarting)
        OperationRecord.create_by_resource(train).insert()
        return train

    def get_status_by_uuids(self, uuids: List[str]) -> Sequence[tuple[str, str, datetime, str, str]]:
        """
        获取资源组状态
        :param uuids:
        :return:
        """
        stmt = select(Train.uuid, Train.status, Train.updated_at, Train.name, Train.reason).where(Train.uuid.in_(uuids)) # noqa
        rg_status: Sequence[tuple[str, str, datetime, str, str]] = self.session.exec(stmt).all()
        return rg_status

    def save_trains(self, trains: Iterable[TrainRead]):
        """
        保存作业
        :param trains:
        :return:
        """
        # 获取作业资源使用情况
        train_ids = list(set([train.uuid for train in trains]))
        pods_name = es_client.get_pods_name_by_lables_app(train_ids)
        metrics_options = MonitoringQueryParams(resource_filer=pods_name, step="5m",
                                                start_at=(datetime.now() - timedelta(days=14)).timestamp(),
                                                end_at=datetime.now().timestamp())
        for train in trains:
            gpu_vendor = train.get_gpu_vendor()
            if gpu_vendor and gpu_vendor != "VGPU":
                metrics_options.gpu_vendor = gpu_vendor
                break
        metrics_index = ["pod_cpu_util", "pod_memory_util", "pod_gpu_util", "pod_gpu_mem_util"]
        prometheus_res = MonitoringOperator().get_named_metrics(metrics_index, metrics_options)
        prometheus_m = {x["metric_name"]: x["result"] for x in prometheus_res}

        # FORMAT datetime microsecond, pod_name, log
        name = str(uuid.uuid4())
        path = get_tmp_file_full_path(name)
        # path = os.path.join("/tmp", f'{self.user.user_id}/trains-{datetime.now().strftime("%Y%m%d%H%M%S")}.csv')
        # if not os.path.exists(os.path.dirname(path)):
        #     os.makedirs(os.path.dirname(path))

        with open(path, "w", encoding="utf-8") as f:
            csv_writer = csv.writer(f)
            csv_writer.writerow(export_headers)
            for train in trains:
                csv_writer.writerow(self.get_export_list_row(train, prometheus_m))

        return name

    def get_export_list_row(self, train: TrainRead, prometheus_m):
        return [
            train.uuid, train.train_replica_specs[0].rg_name, train.train_replica_specs[0].rg_id,
            train.project_category, train.name, train_status_zhCN.get(train.status), train.get_replica_specs_summary(),
            sum([x.replicas for x in train.train_replica_specs]), train.priority, train.image,
            strftime(train.created_at), strftime(train.running_at), strftime(train.ended_at),
            train.user_name, train.user_id, train.email,
            convert_seconds_to_h_m_s(train.get_running_time()),
            convert_seconds_to_hour(train.get_gpu_card_time()),
            convert_seconds_to_hour(train.get_cpu_time()),
            # cpu 平均利用率
            to_persent_str(
                round(mean([float(x["avg_value"]) for x in prometheus_m.get("pod_cpu_util", []) if
                            x["metric"]["pod"].startswith(train.uuid)] or [0]), 5)),
            # 内存 平均利用率
            to_persent_str(
                round(mean([float(x["avg_value"]) for x in prometheus_m.get("pod_memory_util", []) if
                            x["metric"]["pod"].startswith(train.uuid)] or [0]), 5)),
            # GPU 平均利用率
            to_persent_str(
                round(mean([float(x["avg_value"]) for x in prometheus_m.get("pod_gpu_util", []) if
                            x["metric"]["pod"].startswith(train.uuid)] or [0]), 5) / 100),
            # CPU显存 平均利用率
            to_persent_str(
                round(mean([float(x["avg_value"]) for x in prometheus_m.get("pod_gpu_mem_util", []) if
                            x["metric"]["pod"].startswith(train.uuid)] or [0]), 5))
        ]

    def save_trains_in_back_ground_task(self, trains, task: TaskService):
        """
        异步任务保存作业, 使用background task
        :param task:
        :param trains:
        :return:
        """
        # 根据 train 的gpu进行分组
        groups: DefaultDict = defaultdict(list)
        list([groups[train.get_gpu_vendor()].append(train) for train in trains])

        metrics_options = MonitoringQueryParams(resource_filer=None, step="5m",
                                                start_at=(datetime.now() - timedelta(days=14)).timestamp(),
                                                end_at=datetime.now().timestamp())
        metrics_index = ["pod_cpu_util", "pod_memory_util", "pod_gpu_util", "pod_gpu_mem_util"]

        # FORMAT datetime microsecond, pod_name, log
        name = str(uuid.uuid4())
        path = get_tmp_file_full_path(name)
        # path = os.path.join("/tmp", f'{self.user.user_id}/trains-{datetime.now().strftime("%Y%m%d%H%M%S")}.csv')
        # if not os.path.exists(os.path.dirname(path)):
        #     os.makedirs(os.path.dirname(path))

        with open(path, "w", encoding="utf-8-sig") as f:
            csv_writer = csv.writer(f)
            csv_writer.writerow(export_headers)
            # progress from 10 to 90
            progress_step = (80 // len(groups)) if groups else 0
            for vendor, group in groups.items():
                for trains_chunk in split_list(group):
                    # 获取作业资源使用情况
                    train_ids = list(set([train.uuid for train in trains_chunk]))
                    metrics_options.resource_filer = es_client.get_pods_name_by_lables_app(train_ids)
                    metrics_options.gpu_vendor = vendor
                    prometheus_res = MonitoringOperator().get_named_metrics(metrics_index, metrics_options)
                    prometheus_m = {x["metric_name"]: x["result"] for x in prometheus_res}
                    for train in trains_chunk:
                        csv_writer.writerow(self.get_export_list_row(train, prometheus_m))
                task.update_progress(task.task_info.progress + progress_step)
        return name

    def get_tasks(self, user: QingcloudUser,  query: DownloadExportQueryParams) -> List[ExportRecord]:
        # 如果没有.where(ExportRecord.id > 0) 条件, 则count_over_all_by_stmt会自动过滤from条件, 导致统计失败
        stmt = select(ExportRecord).where(ExportRecord.id > 0)
        if not user.is_super_user():
            stmt = stmt.where(or_(ExportRecord.user_id == user.user_id, ExportRecord.root_user_id == user.user_id))
        elif query.owner:
            stmt = stmt.where(or_(ExportRecord.user_id == query.owner, ExportRecord.root_user_id == query.owner))

        if query.start_time:
            stmt = stmt.where(ExportRecord.created_at >= query.start_time)
        if query.end_time:
            stmt = stmt.where(ExportRecord.created_at <= query.end_time)
        if query.status:
            stmt = stmt.where(ExportRecord.status.in_(query.status))

        count = ExportRecord.count_over_all_by_stmt(stmt, session_=self.session)

        stmt = stmt.order_by(desc(ExportRecord.created_at)).offset((query.page -1) * query.size).limit(query.size)

        return self.session.scalars(stmt).all(), count
