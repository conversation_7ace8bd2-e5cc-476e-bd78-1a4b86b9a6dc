import asyncio
import itertools
import os
import random
import re
import string
import time
import uuid
from datetime import datetime, timedelta
from enum import Enum
from functools import lru_cache, wraps
from hashlib import md5
from typing import Dict, Optional

import asyncssh
from cachetools import TTLCache
from kr8s._objects import Node
from sqlalchemy import inspect
import app
from app import logger
from app.core.async_pool import get_async_pool
from app.core.constant import RESOURCE_NOTEBOOK, RESOURCE_TRAIN
from app.core.qingcloud.common import send_to_push_server

namespace_cache = TTLCache(maxsize=128, ttl=60)

NAMESPACE_AICP = uuid.UUID("09bb087f-ba97-4a2d-96d1-13539359cd88")


@lru_cache()
def get_hash(s: str) -> str:
    """
    根据挂载路径生成hash
    :param mount_path:
    :return:
    """
    return str(uuid.uuid5(NAMESPACE_AICP, s))


def generate_random_string(length=8):
    characters = string.ascii_letters + string.digits  # 包含字母和数字
    random_string = ''.join(random.choice(characters) for i in range(length))
    return random_string.lower()


def get_product_resource_info(filters):
    '''
    获取产品配置信息
    :return: dict
    '''
    conf = {
        "gpu": 0,
        "gpu_memory": 0,
        "cpu_model": '',
        "gpu_model": '',
        "gpu_name": '',
        "nvlink": False,
        "network": 0,
        "disk": 0,
        "os_disk": 0,
        "node_type": ''
    }
    for attr in filters:
        if attr["attr_id"] == "cpu_count":
            conf["cpu"] = int(attr["attr_value"])
        elif attr["attr_id"] == "cpu_model":
            conf["cpu_model"] = attr["attr_value"]
        elif attr["attr_id"] == "memory":
            conf["memory"] = int(attr["attr_value"])
        elif attr["attr_id"] == "gpu_count":
            conf["gpu"] = int(attr["attr_value"])
        elif attr["attr_id"] == "gpu_memory":
            conf["gpu_memory"] = int(attr["attr_value"])
        elif attr["attr_id"] == "gpu_model":
            conf["gpu_model"] = attr["attr_value"]
            conf["gpu_name"] = attr["name"]
        elif attr["attr_id"] == "nvlink":
            conf["nvlink"] = True if attr["attr_value"] else False
        elif attr["attr_id"] == "network":
            conf["network"] = int(attr["attr_value"])
        elif attr["attr_id"] == "os_disk":
            conf["os_disk"] = int(attr["attr_value"])
        elif attr["attr_id"] == "disk":
            conf["disk"] = int(attr["attr_value"])
        elif attr["attr_id"] == "aipods_usage":
            conf["node_type"] = attr["name"]
    return conf


def get_product_aipods_type(filters):
    for attr in filters:
        if attr["attr_id"] == "aipods_type":
            return attr["attr_value"]


def get_product_gpu_model(filters):
    for attr in filters:
        if attr["attr_id"] == "gpu_model":
            return attr["attr_value"]


def get_gpu_sorted_key(sku):
    data = sku["filters"]
    for i in range(len(data)):
        if data[i]["attr_id"] == "gpu_count":
            return int(data[i]["attr_value"])
    return 0


B64_TABLE = "0123456789abcdefghijklmnopqrstuvwxyz"


def decimal_to_base36(decimal_num: int):
    if decimal_num == 0:
        return '0'

    base36_num = ''

    while decimal_num > 0:
        remainder = decimal_num % 36
        base36_num = B64_TABLE[remainder] + base36_num
        decimal_num //= 36

    return base36_num


# snowflake id generate

# 64位ID的划分
WORKER_ID_BITS = 5
DATACENTER_ID_BITS = 5
SEQUENCE_BITS = 12

# 最大取值计算
MAX_WORKER_ID = -1 ^ (-1 << WORKER_ID_BITS)  # 2**5-1 0b11111
MAX_DATACENTER_ID = -1 ^ (-1 << DATACENTER_ID_BITS)

# 移位偏移计算
WORKER_ID_SHIFT = SEQUENCE_BITS
DATACENTER_ID_SHIFT = SEQUENCE_BITS + WORKER_ID_BITS
TIMESTAMP_LEFT_SHIFT = SEQUENCE_BITS + WORKER_ID_BITS + DATACENTER_ID_BITS

# 序号循环掩码
SEQUENCE_MASK = -1 ^ (-1 << SEQUENCE_BITS)

# Twitter元年时间戳
TWEPOCH = 1288834974657


class IdWorker(object):
    """
    用于生成IDs
    """

    def __init__(self, datacenter_id, worker_id, sequence=0):
        """
        初始化
        :param datacenter_id: 数据中心（机器区域）ID
        :param worker_id: 机器ID
        :param sequence: 其实序号
        """
        # sanity check
        if worker_id > MAX_WORKER_ID or worker_id < 0:
            raise ValueError('worker_id值越界')

        if datacenter_id > MAX_DATACENTER_ID or datacenter_id < 0:
            raise ValueError('datacenter_id值越界')

        self.worker_id = worker_id
        self.datacenter_id = datacenter_id
        self.sequence = sequence

        self.last_timestamp = -1  # 上次计算的时间戳

    def _gen_timestamp(self):
        """
        生成整数时间戳
        :return:int timestamp
        """
        return int(time.time() * 1000)

    def get_id(self):
        """
        获取新ID
        :return:
        """
        timestamp = self._gen_timestamp()

        # 时钟回拨
        if timestamp < self.last_timestamp:
            logger.error('clock is moving backwards. Rejecting requests until {}'.format(self.last_timestamp))
            raise RuntimeError('clock is moving backwards. Rejecting requests until {}'.format(self.last_timestamp))

        if timestamp == self.last_timestamp:
            self.sequence = (self.sequence + 1) & SEQUENCE_MASK
            if self.sequence == 0:
                timestamp = self._til_next_millis(self.last_timestamp)
        else:
            self.sequence = 0

        self.last_timestamp = timestamp

        new_id = ((timestamp - TWEPOCH) << TIMESTAMP_LEFT_SHIFT) | (self.datacenter_id << DATACENTER_ID_SHIFT) | \
                 (self.worker_id << WORKER_ID_SHIFT) | self.sequence
        return new_id

    def get_hex_id(self):
        """
        获取16进制的新id
        :return:
        """
        # return hex(self.get_id())[2:]
        return decimal_to_base36(self.get_id())

    def _til_next_millis(self, last_timestamp):
        """
        等到下一毫秒
        """
        timestamp = self._gen_timestamp()
        while timestamp <= last_timestamp:
            timestamp = self._gen_timestamp()
        return timestamp


id_worker = IdWorker(1, 1, os.getpid())

zhCN = {
    RESOURCE_NOTEBOOK: "创建容器实例",
    RESOURCE_TRAIN: "创建训练任务",
}

suspend_action_zhCN = {
    RESOURCE_NOTEBOOK: "暂停容器实例",
    RESOURCE_TRAIN: "暂停训练任务",
}

delete_action_zhCN = {
    RESOURCE_NOTEBOOK: "删除容器实例",
    RESOURCE_TRAIN: "停止训练任务",
}

zhCN_status = {
    "Completed": "完成",
    "Succeeded": "成功",
    "Failed": "失败",
    "Deleted": "已删除",
}


def receive_after_update(mapper, connection, target):
    try:
        insp = inspect(target).get_history("status", True)
        if not insp.has_changes():
            # if status not change, not push event to push server
            return

        logger.debug(f"receive_after_update, push event to push server, [uuid={target.uuid},status={target.status}]")
        action = ""
        if target.status in ["Pending", "Creating", "Created", "Running", "CreateFailed", "Inqueuing"]:
            action = zhCN[target.__tablename__]
        elif target.status in ["Suspending", "Suspended"]:
            action = suspend_action_zhCN[target.__tablename__]
        elif target.status in ["Terminating", "Terminated"]:
            action = delete_action_zhCN[target.__tablename__]
        elif target.__tablename__ == "train" and target.ended_at and target.status in zhCN_status:
            # 推送分布式训练任务
            action = "分布式训练任务: %s" % zhCN_status.get(target.status)
        if action:
            async_pool = get_async_pool()
            async_pool.submit_sync(
                send_to_push_server,
                action, target.uuid, target.status, target.user_id, target.__tablename__, target.reason, target.uuid,
                callback=None
            )

    except Exception as e:
        logger.error("push event to push server error, %s", e)


exclude_complicated_urls = [
    re.compile(x) for x in app.settings.exclude_url
]


def is_exclude_url(url: str) -> bool:
    """
    判断 url 是否在排除列表中, 使用正则表达式进行验证
    """
    for pattern in exclude_complicated_urls:
        if pattern.match(url):
            return True
    return False


def to_k8s_rfc_1123(name):
    # 删除非允许的字符
    name = re.sub(r'[^a-z0-9-]', '', name.lower())
    # 删除开头和结尾的连字符
    name = re.sub(r'^-|-$', '', name)
    # 确保名字长度不超过63个字符
    name = name[:63]
    return name


def k8s_to_prometheus_resource_name(k8s_name: str) -> str:
    # 将资源名称转换为小写
    prometheus_name = k8s_name.lower()

    # 将'-'和'.'替换为'_'
    prometheus_name = re.sub(r'[-/.]', '_', prometheus_name)

    # 如果有特殊字符，可以按需添加处理逻辑
    # prometheus_name = re.sub(r'[^a-z0-9_]', '', prometheus_name)

    return prometheus_name


class GPUMatcher():
    def __init__(self, vendor, match, label, resource, virtual=None):
        self.vendor = vendor
        self.match = match
        self.label = label
        self.resource = resource
        self.virtual = virtual
        self.match_compiled = re.compile(match)
        self.resource_rfc_1123 = to_k8s_rfc_1123(resource)
        self.resource_name_in_prometheus = k8s_to_prometheus_resource_name(self.resource)

    def matcher(self, key):
        return self.match_compiled.match(key)

    def __repr__(self):
        return f"GPUMatcher({self.vendor}, {self.match}, {self.label}, {self.resource})"


class GPUManager():
    _instance = None  # 类变量，存储单例实例

    def __new__(cls, *args, **kwargs):
        # 如果实例不存在，则创建一个新的实例
        if cls._instance is None:
            cls._instance = super(GPUManager, cls).__new__(cls)
        return cls._instance

    def __init__(self):
        # 防止重复初始化
        if hasattr(self, '_initialized') and self._initialized:
            return
        self.matchers = []
        for vendor in app.settings.GPU_MAP:
            self.matchers.extend([GPUMatcher(vendor, **gpu) for gpu in app.settings.GPU_MAP[vendor]])

    def get_gpu_matcher(self, key) -> Optional[GPUMatcher]:
        for matcher in self.matchers:
            if matcher.matcher(key):
                return matcher
        return None

    def get_gpu_matcher_by_resource_rfc_1123(self, name) -> Optional[GPUMatcher]:
        name_to_k8s_rfc_1123 = to_k8s_rfc_1123(name)
        for matcher in self.matchers:
            if matcher.resource_rfc_1123 == name_to_k8s_rfc_1123:
                return matcher
        return None

    def get_node_gpu_type(self, node_allocatable: Dict) -> Optional[GPUMatcher]:
        for matcher in self.matchers:
            if matcher.resource in node_allocatable and int(node_allocatable[matcher.resource]) > 0:
                return matcher
        return None

    def get_all_rfc_1123_resource(self):
        return [matcher.resource_rfc_1123 for matcher in self.matchers]

    def get_all_resource_name_in_prometheus(self):
        return [matcher.resource_name_in_prometheus for matcher in self.matchers if matcher.vendor != 'VGPU']


gpu_manager = GPUManager()


def get_models_pk_v(models):
    """
    get the sqlmodel value of  primary key
    :param models: a list of models
    :return:
    """
    if not models:
        return []
    pks = models[0].__table__.primary_key.columns.keys()
    if pks:
        return [getattr(model, pks[0]) for model in models]
    return []


def get_model_pk_v(model):
    """
    get the sqlmodel value of  primary key
    :param model: a model
    :return:
    """
    pks = model.__table__.primary_key.columns.keys()
    if pks:
        return getattr(model, pks[0])
    return None


def nano_to_datetime(nano_timestamp):
    # 将纳秒级别的时间转换为秒
    seconds = nano_timestamp / 1_000_000_000
    # 使用datetime.from timestamp来创建datetime对象
    return datetime.fromtimestamp(seconds)


def datetime_to_nano(dt):
    # 将datetime对象转换为纳秒级别的时间
    return int(dt.timestamp() * 1_000_000_000)


def log_background_task_exception(func):
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            logger.exception(f"background task error: {e}")

    return wrapper


def cover_to_gb(input_string):
    match = re.match(r'^(\d+)', input_string)
    if match:
        values = match.group(1)
        if input_string[-2:].upper() == "KI":
            return int(values) / 1024 / 1024
        elif input_string[-2:].upper() == "MI":
            return int(values) / 1024
        elif input_string[-2:].upper() == "GI":
            return int(values)
        else:
            return int(values) / 1024 / 1024 / 1024
    else:
        return 0


def get_number(input_string):
    match = re.match(r'^(\d+)', input_string)
    if match:
        return int(match.group(1))
    else:
        return 0


def convert_seconds_to_h_m_s(seconds):
    if not seconds:
        return "0小时0分钟0秒"
    m, s = divmod(seconds, 60)
    h, m = divmod(m, 60)
    return f"{int(h)}小时{int(m)}分钟{int(s)}秒"


def convert_seconds_to_hour(seconds: int) -> float:
    """
    将秒转换为小时, format dd.dd
    :param seconds:
    :return:
    """
    if not seconds:
        return 0.0
    return round(seconds / 3600.00, 2)


def to_persent_str(num):
    return f"{num * 100:.2f}%"


def strftime(datetime_obj, format_str="%Y-%m-%d %H:%M:%S"):
    if not datetime_obj:
        return ""
    return datetime_obj.strftime(format_str)


def split_list(lst, chunk_size=100):
    """
    Split a list into chunks
    :param lst:
    :param chunk_size:
    :return:

    :example:
    >>> split_list([1, 2, 3, 4, 5, 6, 7, 8], 3)
    [[1, 2, 3], [4, 5, 6], [7, 8]]

    """
    return [list(filter(lambda x: x is not None, chunk)) for chunk in itertools.zip_longest(*[iter(lst)] * chunk_size)]


def convert_value(value: str, type: str) -> any:
    if type == "int":
        try:
            return int(value)
        except ValueError:
            print(f"Error: '{value}' cannot be converted to int.")
            return None
    elif type == "float":
        try:
            return float(value)
        except ValueError:
            print(f"Error: '{value}' cannot be converted to float.")
            return None
    elif type == "bool":
        if value.lower() == "true":
            return True
        elif value.lower() == "false":
            return False
        else:
            print(f"Error: '{value}' cannot be converted to bool.")
            return None
    elif type == "str":
        return value
    else:
        print(f"Error: Unsupported type '{type}'.")
        return None


class AsyncifyMethods:
    """
    一个类装饰器，将类中的同步方法包装为异步方法，使用 asyncio.run_in_executor 执行。
    """

    def __init__(self, cls):
        self.cls = cls

    def __call__(self, *args, **kwargs):
        # 实例化原始类
        instance = self.cls(*args, **kwargs)

        # 遍历类的所有属性和方法
        for attr_name in dir(instance):
            if attr_name.startswith("_"):  # 跳过私有或特殊方法
                continue

            attr = getattr(instance, attr_name)
            if callable(attr) and not asyncio.iscoroutinefunction(attr):
                # 创建独立作用域生成异步方法
                async_method = self._create_async_method(attr)
                setattr(instance, attr_name, async_method)

        return instance

    @staticmethod
    def _create_async_method(method):
        """
        创建独立的异步方法包装
        """

        @wraps(method)
        async def async_wrapper(*args, **kwargs):
            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(None, lambda: method(*args, **kwargs))

        return async_wrapper


def aicp_json_default_serializer(obj):
    """
    JSON 序列化时的默认处理函数
    :param obj:
    :return:
    """
    if isinstance(obj, datetime):
        return obj.strftime("%Y-%m-%d %H:%M:%S")
    return str(obj)


def get_tmp_file_full_path(name: str) -> str:
    if not os.path.exists(app.settings.TMP_DIR):
        os.makedirs(app.settings.TMP_DIR)
    return os.path.join(app.settings.TMP_DIR, name)


def get_tmp_file_url(file_name: str, download_name: str) -> str:
    return f"{app.settings.GLOBAL_SERVER_URL}/aicp/download/{file_name}?zone={app.settings.qingcloud_zone}&download_name={download_name}"


def get_namespaces_cache_key(user_id):
    return f"aicp:profile:namespaces:{user_id}"


def get_notebook_server_cache_key(uuid):
    return f"aicp:notebook:server:{uuid}"


async def get_node_ip(name):
    node = await Node.get(name)
    for addr in node.status.addresses:
        if addr.type == "InternalIP":
            return addr.address


async def get_node_ip_and_runtime(name):
    node = await Node.get(name)
    ip = ""
    runtime = node.status.nodeInfo.containerRuntimeVersion.split(":")[0]
    for addr in node.status.addresses:
        if addr.type == "InternalIP":
            ip = addr.address
    return ip, runtime


class TransferImageStatus(str, Enum):
    Running = "Running"
    Success = "Success"
    Failed = "Failed"


class ImageLoadStatus(str, Enum):
    FileNotExists = "FileNotExists"
    LoadError = "LoadError"
    LoadSuccess = "LoadSuccess"


async def load_image(host, username, path, key_path, runtime) -> ImageLoadStatus:
    if runtime == "docker":
        command = f"docker load -i {path}"
    else:
        command = f"ctr -n k8s.io images import {path}"
    try:
        async with asyncssh.connect(
                host=host,
                username=username,
                client_keys=[key_path],
                known_hosts=None  # 根据需要配置known_hosts
        ) as conn:
            # run and check ret code
            dest_sftp = await conn.start_sftp_client()
            if not await dest_sftp.exists(path):
                return ImageLoadStatus.FileNotExists
            result = await conn.run(command, check=True, timeout=3600)
            logger.info(result.stdout.strip())
            return ImageLoadStatus.LoadSuccess
    except Exception as e:
        logger.error(f"Connection error: {e}")
        return ImageLoadStatus.LoadError


class TransferImageTaskManager:
    def __init__(self):
        self.tasks = {}

    def update_task(self, task_id, task_status):
        self.tasks[task_id] = {
            "status": task_status,
            "update_time": datetime.now()
        }

    def get_task(self, task_id):
        return self.tasks.get(task_id, None)

    def del_task(self, task_id):
        if task_id in self.tasks:
            del self.tasks[task_id]

    def expire_task(self):
        for task_id, task_status in self.tasks.items():
            if datetime.now() - task_status["update_time"] > timedelta(minutes=30):
                del self.tasks[task_id]

    @classmethod
    def gen_task_id(cls, source_host: str, dest_host: str, source_path: str, dest_path: str):
        return md5(f"{source_host}{dest_host}{source_path}{dest_path}".encode()).hexdigest()

    async def transfer_and_load_image(
            self,
            # node1 和 node2 的连接信息
            source_host: str,
            dest_host: str,
            source_path: str,
            dest_path: str,
            dest_runtime: str,
            source_username='root',
            dest_username='root',
            key_path='/root/.ssh/id_rsa'  # 替换为master1上实际的密钥路径
    ):
        logger.info(f"Sync image from {source_host}:{source_path} to {dest_host}:{dest_path}")
        task_id = self.gen_task_id(source_host, dest_host, source_path, dest_path)
        # 检查dest_host存在dest_path, 如果存在直接load
        load_status = await load_image(dest_host, dest_username, dest_path, key_path, dest_runtime)
        if load_status == ImageLoadStatus.LoadSuccess:
            logger.info(f"Image {dest_path} already exists on {dest_host}. Skipping transfer.")
            self.update_task(task_id, TransferImageStatus.Success)
            return
        elif load_status == ImageLoadStatus.LoadError:
            logger.info(f"Image {dest_path} load failed on {dest_host}. Skipping transfer.")
            self.update_task(task_id, TransferImageStatus.Failed)

        # 在node1上创建临时密钥文件名
        temp_key_name = f".key_{uuid.uuid4().hex}"
        temp_key_path = f"/tmp/{temp_key_name}"

        # 初始化SFTP客户端为None，便于finally块检查
        sftp = None
        conn = None

        try:
            # 从master1连接到node1
            conn = await asyncssh.connect(host=source_host, username=source_username, client_keys=[key_path], known_hosts=None)
            sftp = await conn.start_sftp_client()

            # 将master1的密钥复制到node1的临时路径
            await sftp.put(key_path, temp_key_path)
            await sftp.chmod(temp_key_path, 0o600)

            # 构造node1上执行的scp命令，使用临时密钥传输文件到node2
            scp_command = (
                f"scp -i {temp_key_path} {source_path} {dest_username}@{dest_host}:{dest_path} "
            )

            # 在node1上执行scp命令
            result = await conn.run(scp_command, check=True, timeout=3600*2)
            logger.info(f"File transferred from {source_host}:{source_path} to {dest_host}:{dest_path}. ")
            if result.stdout.strip():
                logger.info(f"Command output: {result.stdout.strip()}")

            # 检查dest_host存在dest_path, 如果存在直接load
            load_status = await load_image(dest_host, dest_username, dest_path, key_path)
            if load_status == ImageLoadStatus.LoadSuccess:
                logger.info(f"Image {dest_path} already exists on {dest_host}. Skipping transfer.")
                self.update_task(task_id, TransferImageStatus.Success)
            else:
                logger.info(f"Image {dest_path} load failed on {dest_host}. Skipping transfer.")
                self.update_task(task_id, TransferImageStatus.Failed)
        except Exception as e:
            logger.error(f"SCP transfer failed: {str(e)}")
            self.update_task(task_id, TransferImageStatus.Failed)
        finally:
            # 清理node1上的临时密钥文件
            if sftp is not None:
                try:
                    # 检查临时密钥文件是否存在
                    if await sftp.exists(temp_key_path):
                        await sftp.remove(temp_key_path)
                        logger.info(f"Temporary key {temp_key_path} removed from {source_host}.")
                except Exception as e:
                    logger.error(f"Failed to remove temporary key {temp_key_path}: {e}")

            # 关闭SFTP和SSH连接
            if sftp is not None:
                sftp.exit()
            if conn is not None:
                conn.close()
