import math
from datetime import datetime
from statistics import mean
from typing import Dict, <PERSON>, Tuple, Union

from app import logger
from .client import PromethusClient
from .query_params import MonitoringQueryParams


def calculate_prometheus_step(start_time: Union[int, str, datetime],
                              end_time: Union[int, str, datetime],
                              max_points: int = 500) -> Tuple[int, str]:
    """
    Calculate the optimal step size for Prometheus queries based on time range.

    Args:
        start_time: Start time (unix timestamp, ISO string, or datetime object)
        end_time: End time (unix timestamp, ISO string, or datetime object)
        max_points: Maximum number of data points (default: 500)

    Returns:
        Tuple of (step_size_seconds: int, human_readable: str)
    """
    # Convert inputs to timestamps if necessary
    if isinstance(start_time, str):
        start_time = datetime.fromisoformat(start_time.replace('Z', '+00:00')).timestamp()
    elif isinstance(start_time, datetime):
        start_time = start_time.timestamp()

    if isinstance(end_time, str):
        end_time = datetime.fromisoformat(end_time.replace('Z', '+00:00')).timestamp()
    elif isinstance(end_time, datetime):
        end_time = end_time.timestamp()

    # Calculate time range in seconds
    time_range = int(end_time - start_time)

    # Calculate minimum step size needed
    min_step = max(math.ceil(time_range / max_points), 30)

    # Round up to more human-friendly intervals
    # if min_step <= 60:  # Up to 1 minute
    #     step = math.ceil(min_step / 30) * 30  # Round to nearest 10 seconds
    #     readable = f"{step}s"
    # elif min_step <= 300:  # Up to 5 minutes
    #     step = math.ceil(min_step / 60) * 60  # Round to nearest minute
    #     readable = f"{step // 60}m"
    # elif min_step <= 3600:  # Up to 1 hour
    #     step = math.ceil(min_step / 300) * 300  # Round to nearest 5 minutes
    #     readable = f"{step // 60}m"
    # elif min_step <= 86400:  # Up to 1 day
    #     step = math.ceil(min_step / 3600) * 3600  # Round to nearest hour
    #     readable = f"{step // 3600}h"
    # else:  # More than 1 day
    #     step = math.ceil(min_step / 86400) * 86400  # Round to nearest day
    #     readable = f"{step // 86400}d"

    return min_step, "30s"


class MonitoringOperator:
    def __init__(self):
        self.prometheus_client = PromethusClient()

    def _make_pod_expr(self, metric: str, options):
        if "_gpu_" in metric:
            if not options.gpu_vendor:
                return ""
            return prometheusQL_meter_templates_pod_GPU[metric][options.gpu_vendor].format(
                pods='|'.join(options.resource_filer))
        expr: str = prometheusQL_meter_templates[metric]
        workload_selector = ""
        if metric in notebook_system_disk_metrics:
            pod_selector = f"""container=~"{'|'.join(options.resource_filer)}" """
        elif metric in notebook_system_disk_metrics_sum:
            pod_selector = f"""namespace="{options.namespace}" """
        else:
            pod_selector = f"""pod=~"{'|'.join(options.resource_filer)}" """
        return expr.replace("$1", workload_selector).replace("$2", pod_selector)

    def _make_notebook_expr(self, metric, options):
        expr: str = prometheusQL_meter_templates[metric]
        pvc_selector = f"""persistentvolumeclaim=~"{'|'.join(options.resource_filer)}" """
        return expr.replace("$2", pvc_selector)

    def _make_node_expr(self, metric, options):
        if "_gpu_" in metric:
            if not options.gpu_vendor:
                return ""
            return prometheusQL_meter_templates_node_GPU[metric][options.gpu_vendor].format(
                nodes='|'.join(options.resource_filer))
        expr: str = prometheusQL_meter_templates[metric]
        node_selector = f"""node=~"{'|'.join(options.resource_filer)}" """
        return expr.replace("$1", node_selector)

    def make_expr(self, metric: str, options):
        if metric.startswith("pod"):
            return self._make_pod_expr(metric, options)
        elif metric.startswith("notebook_zfs"):
            return self._make_notebook_expr(metric, options)
        elif metric.startswith("node"):
            return self._make_node_expr(metric, options)

    def replace_rate_interval(self, expr: str, start, end, max_points=300):
        # 最小时间为5分钟
        rate_interval = max(int((end - start) / max_points), 300)
        return expr.replace("$__rate_interval", f"{rate_interval}s")

    def get_named_metric(self, metrics: str, options: MonitoringQueryParams):
        res = self.get_named_metrics([metrics], options)
        return res[0] if res else {}
    
    def convert_metric_filter(self,metric_name:str,options:MonitoringQueryParams)->tuple[str,bool]:
        """
        为容器实例列表查询节点信息做特化处理，在pod 上无gpu ，vgpu，vdcu 三种实例，使用pod_cpu_util
        去兜底，获得节点信息，并向前兼容； 有gpu 类型的实例，仍旧用 pod_gpu_util
        Args:
            metric_name (str): 指标类型名
            options (MonitoringQueryParams): 查询相关的选项信息

        Returns:
            tuple[str,bool]: 转换后的指标类型名，以及是否是 node_info 查询指标
        """
        if metric_name=="node_info":
            mode = "pod_cpu_util"
            if options.gpu_vendor and options.gpu_vendor not in ["VGPU","VDCU"]:
                mode = "pod_gpu_util"
            return mode,True
        return metric_name,False

    def process_cpu_result(self,metrics_res)->None:
        metric = metrics_res["metric"]
        pod_keys = ["pod", "exported_pod", "pod_name", "podname", "dcu_pod_name"]
        for key in pod_keys:
            if key in metric:
                metric["pod"] = metric[key]
        namespace_keys = ["namespace", "exported_namespace", "podnamespace", "dcu_pod_namespace"]
        for key in namespace_keys:
            if key in metric:
                metric["namespace"] = metric[key]

        node_keys = ["node", "Hostname", "nodeid", "zone"]
        for key in node_keys:
            if key in metric:
                metric["node"] = metric[key]

    def process_gpu_result(self, metrics_res, options):
        metric = metrics_res["metric"]
        metric["gpu_vendor"] = options.gpu_vendor

        pod_keys = ["pod", "exported_pod", "pod_name", "podname", "dcu_pod_name"]
        for key in pod_keys:
            if key in metric:
                metric["pod"] = metric[key]

        namespace_keys = ["namespace", "exported_namespace", "podnamespace", "dcu_pod_namespace"]
        for key in namespace_keys:
            if key in metric:
                metric["namespace"] = metric[key]

        node_keys = ["node", "Hostname", "nodeid", "zone"]
        for key in node_keys:
            if key in metric:
                metric["node"] = metric[key]

        device_keys = ["gpu", "id", "vdeviceid", "deviceidx", "minor_number"]
        for key in device_keys:
            if key in metric:
                metric["device"] = metric[key]

    def get_named_metrics(self, metrics: List[str], options: MonitoringQueryParams):
        """
        获取指定的指标
        :param metrics:
        :param options:
        :return:
        """
        results = []
        is_query_range: bool = False
        if options.start_at and options.end_at:
            is_query_range = True

        for metrics_name in metrics:
            metrics_name,is_node_info = self.convert_metric_filter(metrics_name,options)
            expr = self.make_expr(metrics_name, options)
            if not expr:
                logger.warning(f"Metrics {metrics_name} not found, skip")
                continue

            if is_query_range:
                step, _ = calculate_prometheus_step(options.start_at, options.end_at)
                logger.info(
                    f"Query range {metrics_name}: {options.start_at} - {options.end_at} - {options.step} : Expr: {expr}")
                expr = self.replace_rate_interval(expr, options.start_at, options.end_at)
                metrics_res = self.prometheus_client.query_range(expr, options.start_at, options.end_at, step)
            else:
                logger.info(f"Query instant {metrics_name} : Expr: {expr}")
                metrics_res = self.prometheus_client.query_prometheus(expr)

            if metrics_res["status"] != "success":
                continue

            if is_query_range:
                # get max,min,avg value
                for metric_r in metrics_res.get("data", {}).get("result", []):
                    metric_r["avg_value"] = round(mean([float(x[1]) for x in metric_r["values"]]), 3)
                    metric_r["max_value"] = round(max([float(x[1]) for x in metric_r["values"]]), 3)
                    metric_r["min_value"] = round(min([float(x[1]) for x in metric_r["values"]]), 3)
                    metric_r["current_value"] = round(float(metric_r["values"][-1][1]), 3)

            rs = metrics_res["data"].get("result", [])
            if "_gpu_" in metrics_name and rs:
                for r in rs:
                    self.process_gpu_result(r, options)
            # 如果是 实例列表用来查询实例所在的节点信息的 metric 类型，需要处理一下查询到的 cpu 特征值
            if is_node_info:
                if metrics_name == "pod_cpu_util":
                    rs = metrics_res["data"].get("result", [])
                    for r in rs:
                        self.process_cpu_result(r)
                # 还原类型
                metrics_name = "node_info"
            metrics_res["data"]["metric_name"] = metrics_name
            results.append(metrics_res["data"])

        return results

    def get_public_metrics(self, metrics: List[str], options: MonitoringQueryParams):
        """
        获取公共指定的指标 重构异构指标后，未来采用公共 metric 避免异构 GPU 指标独立对接
        :param metrics:
        :param options:
        :return:
        """
        results = []
        is_query_range: bool = False
        if options.start_at and options.end_at:
            is_query_range = True

        for item in metrics:
            for metric_name, expr in item.items():
                if is_query_range:
                    step, _ = calculate_prometheus_step(options.start_at, options.end_at)
                    logger.info(
                        f"Query range {expr}: {options.start_at} - {options.end_at} - {step}")
                    metrics_res = self.prometheus_client.query_range(expr, options.start_at, options.end_at, step)
                else:
                    logger.info(f"Query instant {expr} : Expr: {expr}")
                    metrics_res = self.prometheus_client.query_prometheus(expr)

                if metrics_res["status"] != "success":
                    continue

                if is_query_range:
                    # get max,min,avg value
                    for metric_r in metrics_res.get("data", {}).get("result", []):
                        metric_r["avg_value"] = round(mean([float(x[1]) for x in metric_r["values"]]), 3)
                        metric_r["max_value"] = round(max([float(x[1]) for x in metric_r["values"]]), 3)
                        metric_r["min_value"] = round(min([float(x[1]) for x in metric_r["values"]]), 3)
                        metric_r["current_value"] = round(float(metric_r["values"][-1][1]), 3)

                metrics_res["data"]["metric_name"] = metric_name
                results.append(metrics_res["data"])

        return results


prometheusQL_meter_templates: Dict = {
    # pod
    # "pod_cpu_usage": """round(sum by (namespace, pod) (irate(container_cpu_usage_seconds_total{job="kubelet", pod!="", image!=""}[5m])) * on (namespace, pod) group_left(owner_kind, owner_name) kube_pod_owner{$1} * on (namespace, pod) group_left(node) kube_pod_info{$2}, 0.001)""",
    "pod_cpu_usage": """sum by (namespace, pod) (node_namespace_pod_container:container_cpu_usage_seconds_total:sum_irate{$2})""",
    # "pod_cpu_util": """round(sum by (namespace, pod) (irate(container_cpu_usage_seconds_total{job="kubelet", pod!="", image!=""}[5m])) * on (namespace, pod) group_left(owner_kind, owner_name) kube_pod_owner{$1} * on (namespace, pod) group_left(node) kube_pod_info{$2} /  on (namespace, pod) sum by (namespace, pod) (cluster:namespace:pod_cpu:active:kube_pod_container_resource_limits{container!~"istio-proxy", $2}), 0.001)""",
    "pod_cpu_util": """sum by (node,namespace, pod) (node_namespace_pod_container:container_cpu_usage_seconds_total:sum_irate{$2}) / sum by (node,namespace, pod) (kube_pod_container_resource_requests{job="kube-state-metrics", resource="cpu", $2})""",
    # "pod_memory_usage": """sum by (namespace, pod) (container_memory_usage_bytes{job="kubelet", pod!="", image!=""}) * on (namespace, pod) group_left(owner_kind, owner_name) kube_pod_owner{$1} * on (namespace, pod) kube_pod_info{$2}""",
    "pod_memory_usage": """sum by (namespace, pod) (container_memory_working_set_bytes{job="kubelet", metrics_path="/metrics/cadvisor", $2})""",
    # "pod_memory_usage_wo_cache": """sum by (namespace, pod) (container_memory_working_set_bytes{job="kubelet", pod!="", image!=""}) * on (namespace, pod) group_left(owner_kind, owner_name) kube_pod_owner{$1} * on (namespace, pod)  kube_pod_info{$2}""",
    "pod_memory_usage_wo_cache": """sum(container_memory_working_set_bytes{job="kubelet", metrics_path="/metrics/cadvisor", container!="", image!="", $2}) by (pod)""",
    "pod_memory_limit": """sum by (namespace, pod) (cluster:namespace:pod_memory:active:kube_pod_container_resource_limits{container!~"istio-proxy", $2})""",
    # "pod_memory_util": """round(sum by (namespace, pod) (container_memory_working_set_bytes{job="kubelet", pod!="", image!=""}) * on (namespace, pod) group_left(owner_kind, owner_name) kube_pod_owner{$1} * on (namespace, pod)  kube_pod_info{$2} /  on (namespace, pod) sum by (namespace, pod) (cluster:namespace:pod_memory:active:kube_pod_container_resource_limits{container!~"istio-proxy", $2}), 0.001)""",
    "pod_memory_util": """sum by (namespace, pod) (container_memory_working_set_bytes{job="kubelet", metrics_path="/metrics/cadvisor", $2}) / sum by (namespace, pod) (kube_pod_container_resource_limits{job="kube-state-metrics", resource="memory", $2})""",
    # "pod_net_bytes_transmitted": """sum by (namespace, pod) (irate(container_network_transmit_bytes_total{pod!="", interface!~"^(cali.+|tunl.+|dummy.+|kube.+|flannel.+|cni.+|docker.+|veth.+|lo.*)", job="kubelet"}[5m])) * on (namespace, pod) group_left(owner_kind, owner_name) kube_pod_owner{$1} * on (namespace, pod)  kube_pod_info{$2}""",
    "pod_net_bytes_transmitted": """sum(irate(container_network_transmit_bytes_total{$2}[$__rate_interval])) by (pod)""",
    # "pod_net_bytes_received": """sum by (namespace, pod) (irate(container_network_receive_bytes_total{pod!="", interface!~"^(cali.+|tunl.+|dummy.+|kube.+|flannel.+|cni.+|docker.+|veth.+|lo.*)", job="kubelet"}[5m])) * on (namespace, pod) group_left(owner_kind, owner_name) kube_pod_owner{$1} * on (namespace, pod)  kube_pod_info{$2}""",
    "pod_net_bytes_received": """sum(irate(container_network_receive_bytes_total{$2}[$__rate_interval])) by (pod)""",
    "pod_gpu_util": """sum by (Hostname, namespace, pod, gpu)(DCGM_FI_DEV_GPU_UTIL{$2})""",
    # DCGM_FI_DEV_FB_USED 指标单位是MB, 统一转换成字节
    "pod_gpu_mem_usage": """sum by (Hostname, namespace, pod, gpu)(DCGM_FI_DEV_FB_USED{$2}) * 1024 * 1024""",
    "pod_gpu_mem_total": """sum by (Hostname, namespace, pod, gpu)(DCGM_FI_DEV_FB_TOTAL{$2}) * 1024 * 1024""",
    "pod_gpu_mem_util": """sum by (Hostname, namespace, pod, gpu)(DCGM_FI_DEV_FB_USED{$2}) / sum by (Hostname, namespace, pod, gpu)(DCGM_FI_DEV_FB_TOTAL{$2})""",
    "pod_tensor_core_util": """sum by (Hostname, namespace, pod, gpu)(DCGM_FI_PROF_PIPE_TENSOR_ACTIVE{$2})""",

    # pod system disk
    "pod_system_disk_limit_bytes": """container_fs_limit_bytes{$2}""",
    "pod_system_disk_usage_bytes": """container_fs_usage_bytes{$2}""",
    "pod_system_disk_available_bytes": """container_fs_limit_bytes{$2} - container_fs_usage_bytes{$2}""",

    "pod_system_disk_limit_bytes_sum_by_namespace": """sum(container_fs_limit_bytes{$2})""",
    "pod_system_disk_usage_bytes_sum_by_namespace": """sum(container_fs_usage_bytes{$2})""",
    "pod_system_disk_available_bytes_sum_by_namespace": """sum(container_fs_limit_bytes{$2}) - sum(container_fs_usage_bytes{$2})""",

    # notebook disk
    "notebook_zfs_used_bytes": """kubelet_volume_stats_used_bytes{$2}""",
    "notebook_zfs_pvc_capacity_bytes": """kubelet_volume_stats_capacity_bytes{$2}""",
    "notebook_zfs_pvc_available_bytes": """kubelet_volume_stats_available_bytes{$2}""",

    "notebook_zfs_used_bytes_sum_by_namespace": """sum(kubelet_volume_stats_used_bytes{$2})""",
    "notebook_zfs_pvc_capacity_bytes_sum_by_namespace": """sum(kubelet_volume_stats_capacity_bytes{$2})""",
    "notebook_zfs_pvc_available_bytes_sum_by_namespace": """sum(kubelet_volume_stats_available_bytes{$2})""",

    # node
    "node_cpu_usage": """round(node:node_cpu_utilization:ratio{$1} * node:node_num_cpu:sum{$1}, 0.001)""",
    "node_cpu_requests": """sum by (node) (kube_pod_container_resource_requests{resource="cpu", $1})""",
    "node_memory_usage_wo_cache": """node:node_memory_used_bytes:sum{$1}""",
    "node_memory_requests": """sum by (node) (kube_pod_container_resource_requests{resource="memory", $1})""",
    "node_net_bytes_transmitted": """node:node_net_transmit_bytes:sum_irate{$1}""",
    "node_net_bytes_received": """node:node_net_receive_bytes:sum_irate{$1}""",
    "node_gpu_util": """sum by (Hostname, gpu) (DCGM_FI_DEV_GPU_UTIL{pod="", $1})""",
    "node_gpu_mem_usage": """sum by (Hostname, gpu)(DCGM_FI_DEV_FB_USED{pod="", $1}) * 1024 * 1024""",
    "node_gpu_mem_total": """sum by (Hostname, gpu)(DCGM_FI_DEV_FB_TOTAL{pod="", $1}) * 1024 * 1024""",
    "node_tensor_core_util": """sum by (Hostname, gpu)(DCGM_FI_PROF_PIPE_TENSOR_ACTIVE{pod="", $1})""",

}

prometheusQL_meter_templates_pod_GPU: Dict = {
    "pod_gpu_util": {
        "NVIDIA": """sum by (Hostname, namespace, pod, gpu)(DCGM_FI_DEV_GPU_UTIL{{pod=~"{pods}"}})""",
        "ASCEND": """sum by (node, namespace, pod_name, id)(container_npu_utilization{{pod_name=~"{pods}"}})""",
        "HEXAFLAKE": """""",
        "HYGON": """sum by (node, dcu_pod_namespace, dcu_pod_name, minor_number)(dcu_utilizationrate{{dcu_pod_name=~"{pods}"}})""",
        "VGPU": """HostCoreUtilization and on(deviceuuid) (vGPUPodsDeviceAllocated{{podname=~"{pods}"}})""",
        None: """""",
    },
    # DCGM_FI_DEV_FB_USED 指标单位是MB, 统一转换成字节
    "pod_gpu_mem_usage": {
        "NVIDIA": """sum by (Hostname, namespace, pod, gpu)(DCGM_FI_DEV_FB_USED{{pod=~"{pods}"}}) * 1024 * 1024""",
        "ASCEND": """sum by (node, namespace, pod_name, id)(container_npu_used_memory{{pod_name=~"{pods}"}}) * 1024 * 1024""",
        "HEXAFLAKE": """""",
        "HYGON": """sum by (node, dcu_pod_namespace, dcu_pod_name, minor_number)(dcu_usedmemory_bytes{{dcu_pod_name=~"{pods}"}})""",
        "VGPU": """(sum by (zone, podnamespace, podname, vdeviceid)(vGPU_device_memory_usage_in_bytes{{podname=~"{pods}"}})) or on(podname) ( HostGPUMemoryUsage * on(deviceuuid) group_left(podname) (vGPUPodsDeviceAllocated{{podname=~"{pods}"}}))""",
        None: """""",
    },
    "pod_gpu_mem_total": {
        "NVIDIA": """sum by (Hostname, namespace, pod, gpu)(DCGM_FI_DEV_FB_TOTAL{{pod=~"{pods}"}}) * 1024 * 1024""",
        "ASCEND": """sum by (node, namespace, pod_name, id)(container_npu_total_memory{{pod_name=~"{pods}"}}) * 1024 * 1024""",
        "HEXAFLAKE": """""",
        "HYGON": """""",
        "VGPU": """sum by (node, dcu_pod_namespace, dcu_pod_name, minor_number)(dcu_memorycap_bytes{{dcu_pod_name=~"{pods}"}})""",
        None: """sum by (zone, podnamespace, podname, vdeviceid)(vGPU_device_memory_limit_in_bytes{{podname=~"{pods}"}})""",
    },
    "pod_gpu_mem_util": {
        "NVIDIA": """sum by (Hostname, namespace, pod, gpu)(DCGM_FI_DEV_FB_USED{{pod=~"{pods}"}}) / sum by (Hostname, namespace, pod, gpu)(DCGM_FI_DEV_FB_TOTAL{{pod=~"{pods}"}})""",
        "ASCEND": """sum by (node, namespace, pod_name, id)(container_npu_utilization{{pod_name=~"{pods}"}}) / sum by (node, namespace, pod_name, id)(container_npu_total_memory{{pod_name=~"{pods}"}})""",
        "HEXAFLAKE": """""",
        "HYGON": """sum by (node, dcu_pod_namespace, dcu_pod_name, minor_number)(dcu_usedmemory_bytes{{dcu_pod_name=~"{pods}"}}) / sum by (node, dcu_pod_namespace, dcu_pod_name, minor_number)(dcu_memorycap_bytes{{dcu_pod_name=~"{pods}"}})""",
        "VGPU": """""",
        None: """""",

    },
    None: {None: """""", },
}

prometheusQL_meter_templates_node_GPU: Dict = {
    "node_gpu_used_count": {
        "NVIDIA": """count by (Hostname) (sum by (Hostname, gpu) (DCGM_FI_DEV_GPU_UTIL{{ Hostname=~"{nodes}",  pod=~"^(nb|tn|inf)-.*$"}}))""",
        "ASCEND": """count by (node) (sum by (node, id) (npu_chip_info_utilization{{ node=~"{nodes}",  pod_name=~"^(nb|tn|inf)-.*$" }}))""",
        "HEXAFLAKE": """""",
        "HYGON": """""",
        "VGPU": """count by (nodeid) (count by (vdeviceid, nodeid) (GPUDeviceSharedNum{{ nodeid=~"{nodes}" }}))""",
    },
    "node_gpu_util": {
        "NVIDIA": """sum by (Hostname, gpu) (DCGM_FI_DEV_GPU_UTIL{{ Hostname=~"{nodes}" }})""",
        "ASCEND": """sum by (node, id) (npu_chip_info_utilization{{ node=~"{nodes}" }})""",
        "HEXAFLAKE": """""",
        "HYGON": """""",
        "VGPU": """""",
    },
    "node_gpu_util_mean": {
        "NVIDIA": """avg by (Hostname) (sum by (Hostname, gpu) (DCGM_FI_DEV_GPU_UTIL{{ Hostname=~"{nodes}" }}))""",
        "ASCEND": """avg by (node) (sum by (node, id) (npu_chip_info_utilization{{ node=~"{nodes}" }}))""",
        "HEXAFLAKE": """""",
        "HYGON": """""",
        "VGPU": """""",
    },
    # DCGM_FI_DEV_FB_USED 指标单位是MB, 统一转换成字节
    "node_gpu_mem_usage": {
        "NVIDIA": """sum by (Hostname, gpu) (DCGM_FI_DEV_FB_USED{{ Hostname=~"{nodes}" }}) * 1024 * 1024""",
        "ASCEND": """sum by (node, id) (npu_chip_info_hbm_used_memory{{ node=~"{nodes}" }}) * 1024 * 1024""",
        "HEXAFLAKE": """""",
        "HYGON": """""",
        "VGPU": """sum by (Hostname, vdeviceid) (vGPU_device_memory_usage_in_bytes{{ Hostname=~"{nodes}" }})""",
    },
    "node_gpu_mem_total": {
        "NVIDIA": """sum by (Hostname, gpu)  (DCGM_FI_DEV_FB_TOTAL{{ Hostname=~"{nodes}" }}) * 1024 * 1024""",
        "ASCEND": """sum by (node, id) (npu_chip_info_hbm_total_memory{{ node=~"{nodes}" }}) * 1024 * 1024""",
        "HEXAFLAKE": """""",
        "HYGON": """""",
        "VGPU": """sum by (nodeid, deviceidx) (GPUDeviceMemoryLimit{{ nodeid=~"{nodes}" }})""",
    }
}

notebook_zfs_metrics = ["notebook_zfs_used_bytes", "notebook_zfs_pvc_capacity_bytes",
                        "notebook_zfs_pvc_available_bytes"]
notebook_zfs_metrics_sum = ["notebook_zfs_used_bytes_sum_by_namespace",
                            "notebook_zfs_pvc_capacity_bytes_sum_by_namespace",
                            "notebook_zfs_pvc_available_bytes_sum_by_namespace"]
notebook_system_disk_metrics = ["pod_system_disk_limit_bytes", "pod_system_disk_usage_bytes",
                                "pod_system_disk_available_bytes"]
notebook_system_disk_metrics_sum = ["pod_system_disk_limit_bytes_sum_by_namespace",
                                    "pod_system_disk_usage_bytes_sum_by_namespace",
                                    "pod_system_disk_available_bytes_sum_by_namespace"]

train_pod_metrics = ["pod_cpu_util", "pod_memory_util", "pod_memory_usage_wo_cache", "pod_memory_limit", "pod_gpu_util",
                     "pod_gpu_mem_usage", "pod_gpu_mem_total", "pod_gpu_mem_util"]

resource_node_metrics = [
    "node_cpu_usage", "node_memory_usage_wo_cache", "node_gpu_used_count", "node_gpu_util", "node_gpu_mem_usage",
    "node_gpu_mem_total", "node_cpu_requests", "node_memory_requests", "node_gpu_util_mean"
]
