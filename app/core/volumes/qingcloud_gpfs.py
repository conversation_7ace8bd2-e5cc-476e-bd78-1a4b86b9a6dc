import hashlib
import os
from typing import List, Union

import kr8s
from httpx import HTTPStatusError
from kr8s import NotFoundError
from kr8s.objects import PersistentVolume, PersistentVolumeClaim, APIObject

from app.core.exceptions import NotSupportVolumeTypeException
from app.core.models import QingcloudUser, VolumeSpecBase
from app.core.qingcloud.gpfs import QingcloudGpfsClient
from app import logger
from app.core.volumes.base_volume import BaseVolume
from app.core.volumes.exceptions import FileSetPermissionException, GetGpfsFilesetException
from app.core.volumes.objects import StorageClass
import app


class GPFS_BACKEND:
    HOSTPATH = "HOSTPATH"
    SUGON = "SUGON"
    GPFS = "GPFS"


def format_quota(quota: str) -> str:
    """
    format quota
    :param quota: quota string
    :return: quota string
    """
    if not quota:
        raise ValueError("quota is empty")

    if isinstance(quota, int):
        return f"{int(quota / 1024 / 1024 / 1024)}Gi"

    unit_map = {
        "MB": "Mi",
        "GB": "Gi",
        "TB": "Ti",
        "PB": "Pi"
    }
    quota_unit = quota[-2:]

    if quota_unit not in unit_map:
        raise ValueError(f"quota unit {quota_unit} not supported")

    return f"{int(quota[:-2])}{unit_map[quota_unit]}"


# noinspection PyCallingNonCallable
class QingcloudGpfsVolume(BaseVolume):
    """
    Qingcloud GPFS Volume
    """

    def __init__(self, user: QingcloudUser, namespace: str, volume_spec: VolumeSpecBase, **kwargs):

        if not app.settings.QINGCLOUD_GPFS_ENABLED:
            raise NotSupportVolumeTypeException("GPFS")

        super().__init__(user, namespace, volume_spec, **kwargs)
        self.gpfs_client = QingcloudGpfsClient(self.user)
        self.backend = app.settings.QINGCLOUD_GPFS_BACKEND
        self.quota = "20Gi"

    def get_real_file_set(self):
        return self.volume_spec.file_set.split("/")[0]

    def get_real_sub_path(self):
        if "/" in self.volume_spec.file_set:
            return self.volume_spec.file_set.split("/", 1)[1]

    @property
    def exists(self) -> bool:
        """
        check if volume exists
        :return:
        """
        return self.has_fileset_permission()

    @property
    def storage_class_name(self) -> str:
        """
        storage class name format: qingcloud-gpfs-storage-class-{file_set}
        :return: storage class name for self.get_real_file_set()
        """
        if app.settings.QINGCLOUD_GPFS_BACKEND == "sugon":
            return app.settings.QINGCLOUD_GPFS_SUGON_BACKEND_STORAGE_CLASS_NAME
        return f"qingcloud-gpfs-storage-class-{self.get_real_file_set()}"

    @property
    def pv_name(self) -> str:
        """
        pv name format: qingcloud-gpfs-pv-{file_set}
        :return: pv name for self.get_real_file_set()
        """
        return f"qingcloud-gpfs-pv-{self.get_real_file_set()}"

    @property
    def pvc_name(self) -> str:
        """
        pvc name format: qingcloud-gpfs-pvc-{file_set}
        :return: pvc name for self.get_real_file_set()
        """
        return f"qingcloud-gpfs-pvc-{self.get_real_file_set()}"

    @property
    def volume_name(self) -> str:
        """
        volume name format: to_k8s_rfc_1123
        :return:
        """
        return f"qingcloud-gpfs-{self.get_real_file_set().lower()}-{hashlib.md5(self.volume_spec.file_set.encode()).hexdigest().lower()[:6]}"

    def has_fileset_permission(self) -> bool:
        """
        check if user has permission to access fileset
        will get fileset through Qingcloud GPFS server
        :return:
        """
        try:
            filesets: List[dict] = self.gpfs_client.get_filesets()
        except HTTPStatusError as e:
            logger.error(f"get filesets failed: {e}")
            raise GetGpfsFilesetException(f"GPFS Server Error: {e.response.status_code}")

        fileset = [fileset for fileset in filesets if fileset.get("file_path") == self.get_real_file_set()]
        if fileset:
            self.quota = format_quota(fileset[0].get("quota"))
            self.volume_spec.permission = "rw"
            return True

        try:
            shared_filesets: List = self.gpfs_client.get_be_shared_filesets()
        except HTTPStatusError as e:
            logger.error(f"get filesets failed: {e}")
            raise GetGpfsFilesetException(f"GPFS Server Error: {e.response.status_code}")

        for shared_fileset in shared_filesets:
            if shared_fileset.get("path") == self.get_real_file_set():
                # 由于共享存储无法查看到 quota, 所以默认 20Gi
                self.quota = "20Gi"
                self.volume_spec.permission = "ro" if shared_fileset.get("readonly", True) else "rw"
                # 返回owner
                self.volume_spec.owner = shared_fileset.get("owner")
                return True

        logger.error(f"{self.user.user_id} no permission to access fileset {self.get_real_file_set()}")
        raise FileSetPermissionException(self.get_real_file_set())


    def has_pvc(self) -> Union[None, PersistentVolumeClaim]:
        """
        check if pvc exists
        :param namespace: which namespace to check
        :return: PersistentVolumeClaim object if exists, else None
        """
        try:
            return PersistentVolumeClaim.get(self.pvc_name, namespace=self.namespace)
        except NotFoundError:
            logger.warning(f"not found pvc {self.pvc_name}")

        return None

    def create_storage_class(self) -> APIObject:
        """
        create storage class by self.get_real_file_set(),
        storage class name format: self.storage_class_name

        :return: sc object
        """
        sc = self.has_storage_class()
        if sc:
            logger.debug(f"sotrage class {self.storage_class_name} already exists")
            # # TODO : 如果 status 是 terminating 的话, 建议先报错吧,
            # #  试过好几次, gpfs 的 terminating 状态会一直持续很久
            # if sc.status == "Terminating":
            #     logger.error(f"storage class {self.storage_class_name} is terminating, please wait")
            #     raise RuntimeError(f"storage class {self.storage_class_name} is terminating, please wait")
            return sc

        if app.settings.QINGCLOUD_GPFS_BACKEND == GPFS_BACKEND.SUGON:
            raise NotSupportVolumeTypeException("SUGON STORAGE ClASS NOT EXIST!")

        logger.debug(f"create storage class {self.storage_class_name}")
        sc: APIObject = StorageClass({
            "apiVersion": "storage.k8s.io/v1",
            "kind": "StorageClass",
            "metadata": {
                "name": self.storage_class_name,
                "labels": {
                    "app": "qingcloud-gpfs",
                    "userid": self.user.user_id
                }
            },
            "provisioner": "spectrumscale.csi.ibm.com",
            "parameters": {
                "volBackendFs": app.settings.QINGCLOUD_GPSE_FILESYSTEM,
                "uid": "1000",
                "gid": "1000",
                "filesetType": "dependent",
                "parentFileset": self.get_real_file_set(),
            },
            "reclaimPolicy": "Retain"
        })
        sc.create()
        return sc

    # noinspection PyCallingNonCallable
    def create_pv(self) -> APIObject:
        """
        create pv by self.get_real_file_set(),
        pv name format: self.pv_name

        :param namespace: which namespace to create pv

        :return: pv object
        """
        pv = self.has_pv()
        if pv is not None:
            logger.debug(f"pv {self.pv_name} already exists")
            return pv

        logger.debug(f"create pv {self.pv_name}")

        pv: APIObject
        if app.settings.QINGCLOUD_GPFS_BACKEND == GPFS_BACKEND.SUGON:
            pv: APIObject = PersistentVolume({
                "apiVersion": "v1",
                "kind": "PersistentVolume",
                "metadata": {
                    "name": self.pv_name,
                    "labels": {
                        "app": "qingcloud-gpfs",
                        "userid": self.user.user_id
                    }
                },
                "spec": {
                    "accessModes": [
                        "ReadWriteMany"
                    ],
                    "capacity": {
                        "storage": self.quota
                    },
                    "csi": {
                        "driver": "parastornas.csi.sugon.com",
                        "volumeAttributes": {
                            "Protocol": app.settings.QINGCLOUD_GPFS_SUGON_BACKEND_PROTOCOL,
                            "StoragePool": app.settings.QINGCLOUD_GPFS_SUGON_BACKEND_STORAGE_POOL,
                            "paramServer": app.settings.QINGCLOUD_GPFS_SUGON_BACKEND_CLUSTER_NAME,
                            "paramShare": app.settings.QINGCLOUD_GPFS_SUGON_BACKEND_FILESYSTEM_ID,
                        },
                        "volumeHandle": f"posix-{app.settings.QINGCLOUD_GPFS_SUGON_BACKEND_STORAGE_POOL}-{app.settings.QINGCLOUD_GPFS_SUGON_BACKEND_FILESYSTEM_ID}-{self.get_real_file_set()}"
                    },
                    "mountOptions": [
                        "user_xattr",
                        "noatim",
                        "acl"
                    ],
                    "persistentVolumeReclaimPolicy": "Delete",
                    "storageClassName": self.storage_class_name,
                    "volumeMode": "Filesystem"
                },
                "status": {
                    "phase": "Bound"
                }
            })
        else:
            pv: APIObject = PersistentVolume({
                "apiVersion": "v1",
                "kind": "PersistentVolume",
                "metadata": {
                    "name": self.pv_name,
                    "namespace": self.namespace,
                    "labels": {
                        "app": "qingcloud-gpfs",
                        "userid": self.user.user_id
                    }
                },
                "spec": {
                    "storageClassName": self.storage_class_name,
                    "capacity": {
                        "storage": self.quota
                    },
                    "accessModes": [
                        "ReadWriteMany"
                    ],
                    "csi": {
                        "driver": "spectrumscale.csi.ibm.com",
                        "volumeHandle": app.settings.QINGCLOUD_GPSE_VOLUME_HANDLE.format(
                            file_set=self.get_real_file_set()),
                    }
                }
            })
        pv.create()
        return pv

    def create_pvc(self) -> APIObject:
        """
        :return: pvc object
        """
        pvc = self.has_pvc()
        if pvc is not None:
            logger.debug(f"pvc {self.pvc_name} already exists")
            return pvc

        logger.debug(f"create pvc {self.pvc_name}")
        pvc: APIObject
        if app.settings.QINGCLOUD_GPFS_BACKEND == GPFS_BACKEND.SUGON:
            pvc: APIObject = PersistentVolumeClaim({
                "apiVersion": "v1",
                "kind": "PersistentVolumeClaim",
                "metadata": {
                    "name": self.pv_name,
                    "namespace": self.namespace,
                    "labels": {
                        "app": "qingcloud-gpfs",
                        "userid": self.user.user_id
                    }
                },
                "spec": {
                    "accessModes": [
                        "ReadWriteMany"
                    ],
                    "resources": {
                        "requests": {
                            "storage": self.quota
                        }
                    },
                    "storageClassName": self.storage_class_name,
                    "volumeMode": "Filesystem",
                    "volumeName": self.pv_name
                }
            })
        else:
            pvc: APIObject = PersistentVolumeClaim({
                "apiVersion": "v1",
                "kind": "PersistentVolumeClaim",
                "metadata": {
                    "name": self.pvc_name,
                    "namespace": self.namespace,
                    "labels": {
                        "app": "qingcloud-gpfs",
                        "userid": self.user.user_id
                    }
                },
                "spec": {
                    "storageClassName": self.storage_class_name,
                    "volumeName": self.pv_name,
                    "accessModes": [
                        "ReadWriteMany"
                    ],
                    "resources": {
                        "requests": {
                            "storage": "10Gi"
                        }
                    }
                }
            })
        pvc.create()
        return pvc

    def delete_storage_class(self):
        """
        delete storage class
        """
        sc = self.has_storage_class()
        if sc:
            logger.debug(f"delete storage class {self.storage_class_name}")
            sc.delete()

    def delete_pv(self):
        """
        delete pv
        """
        pv = self.has_pv()
        if pv:
            logger.debug(f"delete pv {self.pv_name}")
            pv.delete()

    def delete_pvc(self):
        """
        delete pvc
        """
        pvc = self.has_pvc()
        if pvc:
            logger.debug(f"delete pvc {self.pvc_name}")
            pvc.delete()

    def create_storage(self):
        """
        create storage class, pv, pvc

        :exception FileSetPermissionException: if user has no permission to access fileset

        :param namespace:
        """
        self.has_fileset_permission()

        if app.settings.QINGCLOUD_GPFS_BACKEND == GPFS_BACKEND.HOSTPATH:
            logger.debug(f"HOSTPATH backend, skip create storage")
            return

        logger.debug(f"create storage {self.get_real_file_set()} for {self.user}")
        self.create_storage_class()
        self.create_pv()
        self.create_pvc()

    def delete_storage(self):
        """
        delete storage class, pv, pvc,
        No need to verify fileset permissions
        :param namespace:
        """
        pass
        # logger.debug(f"delete storage {self.get_real_file_set()} for {self.user}")
        # self.delete_pvc()
        # self.delete_pv()
        # self.delete_storage_class()

    def get_volume_definition(self):
        if self.backend == GPFS_BACKEND.HOSTPATH:
            return {
                "name": self.volume_name,
                "hostPath": {
                    "path": os.path.join(app.settings.QINGCLOUD_GPSE_HOSTPATH_BACKEND_HOSTPATH, self.volume_spec.file_set),
                    # GPFS 主机必须存在目录才可以挂载
                    "type": self.volume_spec.file_type or "DirectoryOrCreate"
                }
            }
        return {
            "name": self.volume_name,
            "persistentVolumeClaim": {
                "claimName": self.pvc_name
            }
        }

    def get_volume_mounts_definition(self):
        volume_mounts = {
            "mountPath": self.volume_spec.mount_path,
            "name": self.volume_name,
            "readOnly": self.is_read_only()
        }
        return volume_mounts
