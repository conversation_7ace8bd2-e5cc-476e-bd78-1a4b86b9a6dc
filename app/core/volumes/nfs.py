from typing import Union

from kr8s import NotFoundError
from kr8s.objects import APIObject, PersistentVolumeClaim

import app
from app import logger
from app.core.models import QingcloudUser
from app.core.volumes.base_volume import BaseVolume
from app.core.volumes.exceptions import FileSetPermissionException


class NfsVolume(BaseVolume):
    """NFS volume.

    该卷适用于NOTEBOOK的存储用户目录
    """

    def __init__(self, user: QingcloudUser, file_set: str, mount_path: str, *args, **kwargs):
        self.sc_name = app.settings.LOCAL_STORAGE_CLASS
        self.file_set = file_set
        self.mount_path = mount_path
        self.disk_limit = kwargs.get("disk")

    @property
    def pvc_name(self) -> str:
        """
        pvc name format: qingcloud-gpfs-pvc-{file_set}
        :return: pvc name for self.file_set
        """
        return f"{self.sc_name}-{self.file_set}"

    def has_pvc(self, namespace) -> Union[None, PersistentVolumeClaim]:
        """
        check if pvc exists
        :param namespace: which namespace to check
        :return: PersistentVolumeClaim object if exists, else None
        """
        try:
            pvc = PersistentVolumeClaim.get(self.pvc_name, namespace=namespace)
            if pvc:
                return pvc
        except NotFoundError:
            logger.warning(f"not found pvc {self.pvc_name}")

        return None

    def create_pvc(self, namespace: str) -> APIObject:
        """
        :return: pvc object
        """
        pvc = self.has_pvc(namespace)
        if pvc:
            logger.debug(f"pvc {self.pvc_name} already exists")
            return pvc

        logger.debug(f"create pvc {self.pvc_name}")
        pvc: APIObject = PersistentVolumeClaim({
            "apiVersion": "v1",
            "kind": "PersistentVolumeClaim",
            "metadata": {
                "name": self.pvc_name,
                "namespace": namespace
            },
            "spec": {
                "accessModes": [
                    "ReadWriteMany"
                ],
                "resources": {
                    "requests": {
                        "storage": "10Gi"
                    }
                },
                "storageClassName": "nfs-csi"
            }
        }
        )
        pvc.create()
        return pvc

    def delete_storage_class(self):
        """
        delete storage class
        """
        sc = self.has_storage_class()
        if sc:
            logger.debug(f"delete storage class {self.storage_class_name}")
            sc.delete()

    def delete_pv(self, namespace: str):
        """
        delete pv
        """
        pv = self.has_pv(namespace)
        if pv:
            logger.debug(f"delete pv {self.pv_name}")
            pv.delete()

    def delete_pvc(self, namespace: str):
        """
        delete pvc
        """
        pvc = self.has_pvc(namespace)
        if pvc:
            logger.debug(f"delete pvc {self.pvc_name}")
            pvc.delete()

    def create_storage(self, namespace: str):
        """
        create storage class, pv, pvc

        :exception FileSetPermissionException: if user has no permission to access fileset

        :param namespace:
        """
        if self.has_fileset_permission() is False:
            logger.error(f"{self.user.user_id} no permission to access fileset {self.file_set}")
            raise FileSetPermissionException(self.file_set)

        logger.debug(f"create storage {self.file_set} for {self.user}")
        self.create_storage_class()
        self.create_pv(namespace)
        self.create_pvc(namespace)

    def delete_storage(self, namespace: str):
        """
        delete storage class, pv, pvc,
        No need to verify fileset permissions
        :param namespace:
        """
        logger.debug(f"delete storage {self.file_set} for {self.user}")
        self.delete_pvc(namespace)
        self.delete_pv(namespace)
        self.delete_storage_class()
