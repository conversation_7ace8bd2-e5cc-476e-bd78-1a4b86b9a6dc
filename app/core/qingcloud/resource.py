import json
import math
from typing import Dict, List, Optional

import app
from app import logger
from app.core.config import IbTypes
from app.core.constant import RG_NODE_TAG_KEY
from app.core.exceptions import NotSupportResoutceTypeException, ResourceNotFoundException
from app.core.qingcloud.billing import QAIBillingService
from app.core.qingcloud.exceptions import BillingServiceException, LeaseFailedException
from app.core.qingcloud.interface import IaasClient
from app.core.utils import gpu_manager


def product_center_query_request(values=None, offset=0, limit=20, search_word=None):
    params = {
        "prod_id": "qai",
        "console_id": app.settings.default_console_id,
        "region_id": [app.settings.default_regin_id],
        "status": ["sale"],
        "field_mask": ["price"],
        "version": "latest",
        "spec_id": "aipods",
        "offset": offset,
        "limit": limit
    }
    if values:
        filters = [
            {"values": values, "code": "aipods_scope"},
        ]
        params["filters"] = filters
    if search_word:
        params["search_word"] = search_word
    req = {
        "action": "ProductCenterQueryRequest",
        "path": "/v1/skus:search",
        "method": "POST",
        "params": json.dumps(params)
    }
    logger.info(req)
    iaas_client = IaasClient(app.settings.qingcloud)
    rsp = iaas_client.send_request("ProductCenterQueryRequest", req)
    return rsp


from dataclasses import dataclass


@dataclass
class ProductCenterResourceProperties:
    name: str
    operator: str
    attr_id: str
    attr_value: str
    usage: int = 2
    value_type: str = "string"

    @property
    def value(self):
        if self.value_type == "number":
            return int(self.attr_value)
        elif self.value_type == "string":
            if self.attr_value:
                return str(self.attr_value)
            return ""
        else:
            return self.attr_value


class ProductCenterResource:

    def __init__(self, specs: str, replicas: int = 1, rg_id=None,
                 custom_properties: Optional[Dict] = None, init_properties: bool = True):
        self.specs = specs
        self.replicas = replicas
        self.rg_id = rg_id
        self.custom_properties = custom_properties
        self.is_resource_group = True

        self.aipods_scope: Optional[ProductCenterResourceProperties] = None
        self.aipods_type: Optional[ProductCenterResourceProperties] = None
        self.aipods_usage: Optional[ProductCenterResourceProperties] = None

        self.cpu_model: Optional[ProductCenterResourceProperties] = None
        self.cpu_count: Optional[ProductCenterResourceProperties] = None
        self.memory: Optional[ProductCenterResourceProperties] = None

        self.gpu_count: Optional[ProductCenterResourceProperties] = None
        self.gpu_memory: Optional[ProductCenterResourceProperties] = None
        self.gpu_model: Optional[ProductCenterResourceProperties] = None
        self.hashrate_allocation: Optional[ProductCenterResourceProperties] = None

        self.os_disk: Optional[ProductCenterResourceProperties] = None
        self.disk: Optional[ProductCenterResourceProperties] = None

        self.network: Optional[ProductCenterResourceProperties] = None
        self.network_description: Optional[ProductCenterResourceProperties] = None

        self.gpu_list: Optional[ProductCenterResourceProperties] = None

        self.cpu_manufacturer: Optional[ProductCenterResourceProperties] = None
        self.cpu_model: Optional[ProductCenterResourceProperties] = None

        if init_properties:
            self.__init_properties()

    @property
    def billing_enable(self):
        """

        :return:
        """
        return app.settings.billing_enable and self.rg_id is None and self.specs and self.specs.startswith("sku_")

    def init_properties(self):
        self.__init_properties()

    def __init_properties(self):
        if self.rg_id is None and self.specs and self.specs.startswith("sku_"):
            self.__init_sku_properties()
            self.is_resource_group = False
        else:
            self.__init_rg_properties()

    def __init_rg_properties(self):
        # 专属资源组固定标签
        self.aipods_scope = ProductCenterResourceProperties(name="专属资源组", operator="==", attr_id="aipods_scope",
                                                            attr_value=self.custom_properties["custom_aipods_type"])
        self.aipods_type = ProductCenterResourceProperties(name="专属资源组", operator="==", attr_id="aipods_type",
                                                           attr_value=self.custom_properties["custom_aipods_type"])
        self.aipods_usage = ProductCenterResourceProperties(
            name="专属资源组",
            operator="==",
            attr_id="aipods_usage",
            attr_value="resource_group"
        )
        self.network = ProductCenterResourceProperties(name="网络", operator="==", attr_id="network",
                                                       attr_value=self.custom_properties["custom_infiniband"], value_type="number")

        self.cpu_count = ProductCenterResourceProperties(
            name=f"{self.custom_properties['custom_cpu']}核",
            operator="==",
            attr_id="cpu_count",
            attr_value=self.custom_properties['custom_cpu'],
            usage=3,
            value_type="number"
        )
        self.memory = ProductCenterResourceProperties(
            name=f"{self.custom_properties['custom_memory']}G",
            operator="==",
            attr_id="memory",
            attr_value=self.custom_properties['custom_memory'],
            usage=3,
            value_type="number"
        )
        self.gpu_count = ProductCenterResourceProperties(
            name=f"{self.custom_properties['custom_gpu']}",
            operator="==",
            attr_id="gpu_count",
            attr_value=self.custom_properties['custom_gpu'],
            usage=3,
            value_type="number"
        )
        self.gpu_memory = ProductCenterResourceProperties(
            name=f"{self.custom_properties['custom_gpu_memory']}Gi",
            operator="==",
            attr_id="gpu_memory",
            attr_value=self.custom_properties['custom_gpu_memory'],
            usage=3,
            value_type="number"
        )
        self.gpu_model = ProductCenterResourceProperties(name=f"{self.custom_properties['custom_gpu_type']}", operator="==",
                                                         attr_id="gpu_model", attr_value=self.custom_properties['custom_gpu_type'], usage=3)
        self.os_disk = ProductCenterResourceProperties(
            name=f"{self.custom_properties['custom_system_disk_size']}GB",
            operator="==",
            attr_id="os_disk",
            attr_value=self.custom_properties['custom_system_disk_size'],
            usage=0,
            value_type="number"
        )
        self.disk = ProductCenterResourceProperties(
            name=f"{self.custom_properties['custom_data_disk_size']}GB",
            operator="==",
            attr_id="disk",
            attr_value=self.custom_properties['custom_data_disk_size'],
            value_type="number"
        )
        self.gpu_list = ProductCenterResourceProperties(
            name=f"GPU列表",
            operator="==",
            attr_id="gpu_list",
            attr_value=','.join(self.custom_properties['custom_gpu_list']) if self.custom_properties['custom_gpu_list'] else "",
        )
        self.hashrate_allocation = ProductCenterResourceProperties(
            name=f"算力分配",
            operator="==",
            attr_id="hashrate_allocation",
            attr_value=self.custom_properties['custom_hashrate_allocation'],
            value_type="number"
        )
        self.cpu_manufacturer = ProductCenterResourceProperties(
            name=f"cpu厂商",
            operator="==",
            attr_id="cpu_manufacturer",
            attr_value=self.custom_properties['custom_cpu_manufacturer'],
        )
        self.cpu_model = ProductCenterResourceProperties(
            name=f"cpu型号",
            operator="==",
            attr_id="cpu_model",
            attr_value=self.custom_properties['custom_cpu_model'],
        )

    def __init_sku_properties(self):

        self.product_resource = product_center_query_request(search_word=self.specs)
        if self.product_resource["ret_code"] != 0 or int(self.product_resource["total"]) != 1:
            raise ResourceNotFoundException(f"product sku : {self.specs}")

        self.price_info_keys = []

        for production_property in self.product_resource["skus"][0]["filters"]:
            self.price_info_keys.append(production_property["attr_id"])
            setattr(self, production_property["attr_id"], ProductCenterResourceProperties(**production_property))

    def get_billing_price_info(self) -> Dict:
        """
        generate billing price info
        :return:
        """
        res = {"replicas": self.replicas}
        for price_info_key in self.price_info_keys:
            attr = getattr(self, price_info_key)
            if attr:
                res[price_info_key] = attr.attr_value
        return res

    def get_price(self, user_id: str, charge_mode: str, duration: int):
        """
        get price
        :param user_id:
        """
        if self.billing_enable:
            return QAIBillingService().get_price(user_id, self.get_billing_price_info(),
                                                 charge_mode=charge_mode, duration=duration)
        return None

    def check_resource_balance(self, user_id: str, charge_mode: str, duration: int):
        """
        check balance
        """
        if self.billing_enable:
            return QAIBillingService().check_resource_balance("check_resource_id", user_id,
                                                              self.get_billing_price_info(),
                                                              duration=duration)

    def lease(self, resource_id: str, user_id: str, charge_mode: str, duration: int, auto_renew: int = 0,
              next_charge_mode: str = None):
        """
        lease resource
        """
        if self.billing_enable:
            QAIBillingService().lease(resource_id, user_id, self.get_billing_price_info(),
                                      charge_mode=charge_mode, duration=duration, check_resource_balance=True,
                                      auto_renew=auto_renew, next_charge_mode=next_charge_mode, count=self.replicas)

    def unlease(self, resource_id: str, user_id: str):
        """

        :param resource_id:
        :param user_id:
        """
        if self.billing_enable:
            try:
                QAIBillingService().unlease(resource_id, user_id)
            except BillingServiceException as e:
                if e.billing_code == 2100:
                    # 2100 未找到租赁信息, 表示已经删除租赁信息
                    return
                raise e

    def is_gpu_product(self) -> bool:
        """
        is gpu product
        :return:
        """
        return self.gpu_model is not None and self.gpu_model.value \
            and self.gpu_count is not None and int(self.gpu_count.value)

    def append_envs(self, envs: List[Dict]):
        exists_names = [env["name"] for env in envs]
        if self.aipods_type.value == "vGPU" and "LIBCUDA_LOG_LEVEL" not in exists_names:
            envs.append({
                "name": "LIBCUDA_LOG_LEVEL",
                "value": "0"
            })

        # fix bug QAI-871 【容器实例】GPU 容器实例制作 image 在 CPU 上无法启动
        if not self.is_gpu_product() and "NVIDIA_VISIBLE_DEVICES" not in exists_names:
            envs.append({
                "name": "NVIDIA_VISIBLE_DEVICES",
                "value": ""
            })

        # If sku has ib.
        if self.network and str(self.network.value) != "0":
            envs.extend([
                {
                    "name": "NCCL_SOCKET_IFNAME",
                    "value": "eth0"
                },
                {
                    "name": "NCCL_DEBUG",
                    "value": "INFO"
                },
            ])

        if self.gpu_list and self.gpu_list.value:
            envs.append({
                "name": "NVIDIA_VISIBLE_DEVICES",
                "value": self.gpu_list.value
            })

    def get_ib_annotations(self, namespace) -> Dict:
        if app.settings.IB_TYPE == IbTypes.ROCE and self.network and str(self.network.value) != "0":
            return {
                "k8s.v1.cni.cncf.io/networks": ",".join(app.settings.ROCE_NETS)
            }
        return None

    def get_product_gpu_type(self) -> str:
        if self._is_share_gpu():
            return "SHARE_GPU"
        elif self._is_vgpu():
            return "vGPU"
        elif self._is_gpu():
            return "GPU"
        else:
            return "CPU"

    def get_k8s_resources_definition(self, oversold: bool = False) -> Dict:
        """
        generate k8s resources definition
        :return:
        """

        resources = {
            "requests": {
                "memory": f"{self.memory.value}Gi",
                "cpu": self.cpu_count.value
            },
            "limits": {
                "memory": f"{self.memory.value}Gi",
                "cpu": self.cpu_count.value
            }
        }

        if oversold:
            resources["requests"] = {
                "memory": f"{int(self.memory.value * 1024 // app.settings.OVERSOLD_RATIO_MEM)}Mi",
                "cpu": f"{int(self.cpu_count.value * 10 // app.settings.OVERSOLD_RATIO * 100)}m"
            }

        if self.gpu_list and self.gpu_list.value:
            #  if gpu_list is not empty,
            #  it means that the user has specified the GPU card,
            #  do not set gpu requirements and not set the cpu and memory requirements
            resources["requests"] = {
                "memory": "1Gi",
                "cpu": "1"
            }
        # set gpu resources
        elif self.is_gpu_product():
            gpu_matcher = gpu_manager.get_gpu_matcher(self.gpu_model.value)
            if self.aipods_type.value == "vGPU":
                if not self.gpu_memory.value:
                    raise NotSupportResoutceTypeException(f"{self.aipods_type}-{self.gpu_memory}")
                resources["requests"]["qingcloud.nvidia.com/vgpu"] = self.gpu_count.value
                resources["limits"]["qingcloud.nvidia.com/vgpu"] = self.gpu_count.value
                resources["requests"]["qingcloud.nvidia.com/vgpumem"] = self.gpu_memory.value * 1000
                resources["limits"]["qingcloud.nvidia.com/vgpumem"] = self.gpu_memory.value * 1000

                if self.hashrate_allocation and self.hashrate_allocation.value:
                    resources["requests"]["qingcloud.nvidia.com/vgpucores"] = self.hashrate_allocation.value
                    resources["limits"]["qingcloud.nvidia.com/vgpucores"] = self.hashrate_allocation.value

            elif self.aipods_type.value == "adapter":
                if not self.gpu_memory.value:
                    raise NotSupportResoutceTypeException(f"{self.aipods_type}-{self.gpu_memory}")

                if self.gpu_count.value != 1:
                    raise NotSupportResoutceTypeException(f"vnpu only support one npu")

                if self.hashrate_allocation and self.hashrate_allocation.value:
                    resources["requests"]["qingcloud.adapter.com/adapter"] = self.hashrate_allocation.value
                    resources["limits"]["qingcloud.adapter.com/adapter"] = self.hashrate_allocation.value
                else:
                    raise NotSupportResoutceTypeException(f"please set vnpu aicores.")

            elif self.aipods_type.value == "vDCU":
                if not self.gpu_memory.value:
                    raise NotSupportResoutceTypeException(f"{self.aipods_type}-{self.gpu_memory}")
                resources["requests"]["qingcloud.hygon.com/dcunum"] = self.gpu_count.value
                resources["limits"]["qingcloud.hygon.com/dcunum"] = self.gpu_count.value
                resources["requests"]["qingcloud.hygon.com/dcumem"] = self.gpu_memory.value * 1000
                resources["limits"]["qingcloud.hygon.com/dcumem"] = self.gpu_memory.value * 1000

                if self.hashrate_allocation and self.hashrate_allocation.value:
                    resources["requests"]["qingcloud.hygon.com/dcucores"] = self.hashrate_allocation.value
                    resources["limits"]["qingcloud.hygon.com/dcucores"] = self.hashrate_allocation.value
            elif gpu_matcher is not None:
                resources["requests"][gpu_matcher.resource] = self.gpu_count.value
                resources["limits"][gpu_matcher.resource] = self.gpu_count.value
                # only gpu product type can request ib card
                if self.network and str(self.network.value) != "0":
                    if app.settings.IB_TYPE == IbTypes.ROCE:
                        for hostdev in app.settings.ROCE:
                            resources["requests"][hostdev] = 1
                            resources["limits"][hostdev] = 1
                    else:
                        resources["requests"]["nvidia.com/hostdev"] = int(self.network.value)
                        resources["limits"]["nvidia.com/hostdev"] = int(self.network.value)
            else:
                raise NotSupportResoutceTypeException(f"{self.aipods_type.value}-{self.gpu_model.value}")

        return resources

    def get_k8s_toleration_configuration(self):
        """
        get k8s toleration configuration
        :return:
        """
        toleration = [
            {
                "key": "aicp.group/worker",
                "operator": "Exists",
            }
        ]

        if self.is_resource_group:
            toleration.append(
                {
                    "key": RG_NODE_TAG_KEY,
                    "operator": "Exists",
                    "effect": "NoSchedule"
                }
            )
            toleration.append({
                "key": "aicp.group/resource_group",
                "operator": "Equal",
                "value": self.rg_id,
                "effect": "NoSchedule"
            })

        return toleration

    def get_k8s_unaffinity_configuration(self):
        pass

    def get_scheduler_name(self):
        if self.aipods_type.value in ["vGPU", "adapter", "vDCU"]:
            return "hami-scheduler"
        if app.settings.VOLCANO_ENABLE and self.aipods_scope.attr_value in ["sharing_compute","job"]:
            return "volcano"
        return "default-scheduler"

    def get_k8s_affinity_configuration(self):
        """
        get k8s affinity configuration
        """
        if self.aipods_type.value == "vGPU":
            # 对于虚拟GPU, 不使用亲和性
            return None

        default = {
            "podAffinity": {
                "preferredDuringSchedulingIgnoredDuringExecution": [
                    {
                        "weight": 100,
                        "podAffinityTerm": {
                            "labelSelector": {
                                "matchExpressions": [
                                    {
                                        "key": "aicp.group/workload",
                                        "operator": "In",
                                        "values": [
                                            "container"
                                        ]
                                    }
                                ]
                            },
                            "namespaceSelector": {},
                            "topologyKey": "kubernetes.io/hostname"
                        }
                    }
                ]
            }
        }
        return default

    def get_k8s_node_selector_configuration(self) -> Dict:
        """
        get k8s node selector configuration
        """
        node_selector = {}

        # 专属资源
        if self.is_resource_group:
            node_selector["aicp.group/resource_group"] = self.rg_id
            if self.specs:
                node_selector["aicp.group/resource_group_node"] = self.specs
        else:
            node_selector["aicp.group/aipods_type"] = self.aipods_type.value

        # GPU卡信息
        if self.is_gpu_product() and not (self.gpu_list and self.gpu_list.value):
            gpu = gpu_manager.get_gpu_matcher(self.gpu_model.value)
            node_selector[gpu.label] = self.gpu_model.value

        # if self.cpu_manufacturer and self.cpu_manufacturer.value:
        #     node_selector["feature.node.kubernetes.io/cpu-model.vendor_id"] = self.cpu_manufacturer.value
        # if self.cpu_model and self.cpu_model.value:
        #     node_selector["feature.node.kubernetes.io/cpu-model.id"] = self.cpu_model.value

        return node_selector

    def _is_share_gpu(self) -> bool:
        if self.gpu_list and self.gpu_list.value:
            return True
        return False

    def _is_vgpu(self) -> bool:
        if self.aipods_type.value in app.settings.VGPU_TAG:
            return True
        return False

    def _is_gpu(self) -> bool:
        if self.is_gpu_product():
            return True
        return False
